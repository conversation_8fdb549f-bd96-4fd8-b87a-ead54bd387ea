package com.tcl.ai.note.handwritingtext.ui.categorydialog.ui

import android.annotation.SuppressLint
import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogController
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogCategory
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogNote
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.getCategoryIconColor
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.globalDialogWidth
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.utils.rememberScreenInfo

// 移动笔记到分类弹窗
@SuppressLint("DesignSystem")
@Composable
internal fun MoveNoteToCategoryDialog(
    toMoveNoteList: List<DialogNote>,
    dialogController: CategoryDialogController,
    inSameCategory: Boolean, //选中的note是否处于同一个分类
    selectCategoryId: Long,//选中的note的分类id,用作高亮
    categories: List<DialogCategory>,
    onMoveToCategory: (Long) -> Unit,
    onDismiss: () -> Unit
) {

    val screenHeight = rememberScreenInfo().height
    val maxHeight = screenHeight - 120
    val maxBoxHeight = maxHeight - 88
    val hasNavigationBar = isButtonNavigation()
//    val navigationBarHeight = getNavigationBarHeight()


    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        ),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable(
                    indication = null, // 禁用点击效果,不然点击背景，背景会变暗
                    interactionSource = remember { MutableInteractionSource() }
                ) { onDismiss() } // 点击背景关闭对话框
                .padding(
                    bottom = if (hasNavigationBar) 16.dp else 16.dp
                )
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .width(globalDialogWidth())
                    .heightIn(188.dp, maxHeight.dp)
                    .clip(RoundedCornerShape(20.dp))
                    .background(colorResource(R.color.bg_dialog))
                    .pointerInput(Unit) {
                        detectTapGestures { /* 空实现，阻止事件传播 */ }
                    }
                    .padding(start = 12.dp, end = 12.dp, bottom = 16.dp, top = 24.dp)
            ) {
                Column {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(min = 0.dp, max = maxBoxHeight.dp)
                    ) {
                        CategoryHeader()
                        CategoryList(
                            categories,
                            selectCategoryId,
                            inSameCategory,
                            toMoveNoteList,
                            onMoveToCategory
                        )
                        Spacer(Modifier.height(16.dp))
                        BottomNewCategoryBtn(onClick = {
                            dialogController.showNewCategoryDialog(true)
                        })
                    }
                }
            }
        }
    }
}

@Composable
private fun ColumnScope.CategoryList(
    categories: List<DialogCategory>,
    selectCategoryId: Long,
    inSameCategory: Boolean,
    toMoveNoteList: List<DialogNote>,
    onMoveToCategory: (Long) -> Unit
) {
    val context = LocalContext.current

    var tip = stringResource(R.string.category_moveto_already)
    LazyColumn(
        modifier = Modifier
            .fillMaxWidth()
            .weight(1f, false) // 填满剩余空间、能滚动
    ) {
        items(categories) { categoryItem ->
            CategoryItem(
                category = categoryItem,
                categoryId = selectCategoryId,
                inSameCategory = inSameCategory,
                onClick = {
                    // 判断是否所有笔记都处于同一个分类
                    val isCanMove = toMoveNoteList.any { note ->
                        note.categoryId != categoryItem.categoryId
                    }
                    val moveSize =
                        toMoveNoteList.count { it.categoryId != categoryItem.categoryId }
                    if (isCanMove) {
                        tip = context.getString(
                            R.string.category_moveto_success_new,
                            toMoveNoteList.size.toString(),
                            categoryItem.name
                        )
                    }
                    Logger.v(
                        TAG,
                        "MoveNoteToCategoryDialog categoryId $selectCategoryId  isCanMove $isCanMove"
                    )
                    if (isCanMove) {
                        onMoveToCategory(categoryItem.categoryId)
                    }
                    ToastUtils.makeWithCancel(tip, Toast.LENGTH_SHORT)
                }
            )
        }
    }
}

@Composable
private fun CategoryHeader() {
    val density = LocalDensity.current
    Column(modifier = Modifier
        .fillMaxWidth()
        .background(colorResource(R.color.bg_dialog))) {
        Text(
            text = stringResource(R.string.choose_category),
            fontSize = with(density) { 20.dp.toSp() },
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.dialog_title),
            modifier = Modifier.padding(start = 12.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CategoryItem(
    category: DialogCategory,
    categoryId: Long,
    inSameCategory: Boolean,
    onClick: () -> Unit,
) {
    val isSelected = inSameCategory && category.categoryId == categoryId
    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (isSelected) colorResource(R.color.bottom_sheet_dialog_drag_bar_color) else Color.Transparent
                ).combinedClickable(
                    role = Role.Button,
                    onClick = {
                        onClick()
                    }
                )
                .padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            category.icon?.let {
                Icon(
                    painter = painterResource(it),
                    contentDescription = category.name,
                    modifier = Modifier
                        .size(24.dp),
                    tint = getCategoryIconColor(category.colorIndex).colorRes()
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            var textColor = colorResource(R.color.text_title)
//            if (isSelected) {
//                textColor = colorResource(R.color.text_category_list_selected)
//            }

            Text(
                text = category.name,
                fontSize = 14.sp,
                color = textColor,
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.width(19.dp))
            Text(
                text = category.noteCounts.toString(),
                fontSize = 14.sp,
                color = colorResource(R.color.text_summary)
            )
        }
    }
}


@Composable
private fun BottomNewCategoryBtn(onClick: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .fillMaxWidth()
            .height(44.dp)
            .clip(RoundedCornerShape(30.dp))
            .clickable(role = Role.Button) {
                onClick()
            }
            .background(
                colorResource(id = R.color.home_category_add_btn_bg)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.dialog_category_name_title),
            color = Color(0xFFFF9800),
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

