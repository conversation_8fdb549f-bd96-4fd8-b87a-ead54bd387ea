package com.tcl.ai.note.handwritingtext.vm.text

import android.text.Layout
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.AlignmentState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarBuilder
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarConfig
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.event.RichTextStyleActionEvent
import com.tcl.ai.note.net.NetworkUseCase
import com.tcl.ai.note.utils.ComposableToast
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 富文本工具栏ViewModel
 * 管理工具栏的配置、状态和行为
 * 待维护一个 richTextEditor 进行富文本编辑操作
 */
@HiltViewModel
class RichTextToolBarViewModel@Inject constructor(
    networkUseCase: NetworkUseCase,
    ) : ViewModel() {
    
    // 工具栏状态
    private val _toolBarState = MutableStateFlow(RichTextToolBarState())
    val toolBarState: StateFlow<RichTextToolBarState> = _toolBarState.asStateFlow()
    
    // 工具栏配置
    private val _toolBarConfig = MutableStateFlow(createDefaultConfig())
    val toolBarConfig: StateFlow<RichTextToolBarConfig> = _toolBarConfig.asStateFlow()
    val isOffline: StateFlow<Boolean> = networkUseCase.isOffline(viewModelScope)
    //控制底部AI弹窗的state
    private val _isShowBottomAIPop = MutableStateFlow(false)
    val isShowBottomAIPop: StateFlow<Boolean> = _isShowBottomAIPop.asStateFlow()

    init {
       viewModelScope.launch {
           RichTextEventManager.toolBarState.collect{ event->
               _toolBarState.value=event
           }
       }
    }
    /**
     * 触发样式事件，给富文本RichTextViewHolder里的EditText
     */
    fun triggerStyleActionEvent(event: RichTextStyleActionEvent, state: RichTextToolBarState) {
        RichTextEventManager.updateManagerStyleState(state)
        RichTextEventManager.triggerStyleActionEvent(event)
    }
    // 触发操作事件，给富文本RichTextViewHolder里的EditText
    fun triggerOperateActionEvent(event: RichTextOperateEvent) {
        RichTextEventManager.triggerTextOperateEvent(event)
    }


    // 更新样式状态
    fun updateToolBarStyleState(update: (RichTextToolBarState) -> RichTextToolBarState) {
        _toolBarState.value = update(_toolBarState.value)
    }
    /**
     * 应用段落格式
     */
    private fun applyParagraphFormat(type: RichTextToolType) {
        // 这里应该调用实际的富文本编辑器API
        // 例如：richTextEditor.applyParagraphFormat(type)
    }

    /**
     * 应用文本格式
     */
    private fun applyTextFormat(type: RichTextToolType) {
        // 这里应该调用实际的富文本编辑器API
        // 例如：richTextEditor.applyTextFormat(type)
    }

    /**
     * 应用颜色格式
     */
    private fun applyColorFormat(type: RichTextToolType, color: Color) {
        // 这里应该调用实际的富文本编辑器API
        // 例如：richTextEditor.applyColorFormat(type, color)
    }

    /**
     * 应用字体大小
     */
    private fun applyFontSize(size: Int) {
        // 这里应该调用实际的富文本编辑器API
        // 例如：richTextEditor.applyFontSize(size)
    }

    /**
     * 应用对齐方式
     */
    private fun applyAlignment(alignment: AlignmentState) {
        // 这里应该调用实际的富文本编辑器API
        // 例如：richTextEditor.applyAlignment(alignment)
    }

    /**
     * 应用缩进
     */
    private fun applyIndent(increase: Boolean) {
        // 这里应该调用实际的富文本编辑器API
        // 例如：richTextEditor.applyIndent(increase)
    }

    /**
     * 创建默认的工具栏配置
     */
    private fun createDefaultConfig(): RichTextToolBarConfig {
        return RichTextToolBarBuilder()
            .addParagraphGroup(
                onTodoToggle = ::handleTodoToggle,
                onBulletListToggle = ::handleBulletListToggle,
                onNumberListToggle = ::handleNumberListToggle
            )
            .addTextFormatGroup(
                onBoldToggle = ::handleBoldToggle,
                onItalicToggle = ::handleItalicToggle,
                onUnderlineToggle = ::handleUnderlineToggle,
                onStrikethroughToggle = ::handleStrikethroughToggle
            )
            .addColorGroup(
                onTextColorChange = ::handleTextColorChange,
//                onTextBgColorChange = ::handleTextBgColorChange,
                onTextColorToggle = ::handleTextColorToggle,
//                onTextBgColorToggle = ::handleTextBgColorToggle
            )
//            .addFontSizeSelector(
//                fontSizes = createFontSizeList(),
//                onSizeChange = ::handleFontSizeChange,
//                onToggleExpanded = ::handleFontSizeToggle
//            )
//            .addAlignmentGroup(
//                onAlignLeftClick = ::handleAlignLeftClick,
//                onAlignCenterClick = ::handleAlignCenterClick,
//                onAlignRightClick = ::handleAlignRightClick
//            )
//            .addIndentGroup(
//                onDecreaseIndent = ::handleIndentDecrease,
//                onIncreaseIndent = ::handleIndentIncrease
//            )
            .build()
    }
    
    // ==================== 段落格式处理 ====================
    
    /**
     * 处理待办事项切换
     */
    fun handleTodoToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                isTodoActive = !currentState.isTodoActive,
                // 互斥逻辑：激活待办时取消其他列表格式
                isBulletListActive = if (!currentState.isTodoActive) false else currentState.isBulletListActive,
                isNumberListActive = if (!currentState.isTodoActive) false else currentState.isNumberListActive
            )
        }
        // 这里可以添加实际的富文本格式化逻辑
        triggerStyleActionEvent(
            RichTextStyleActionEvent.TodoToggled(_toolBarState.value.isTodoActive),
            _toolBarState.value
        )
    }
    
    /**
     * 处理无序列表切换
     */
    fun handleBulletListToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                isBulletListActive = !currentState.isBulletListActive,
                // 互斥逻辑
                isTodoActive = if (!currentState.isBulletListActive) false else currentState.isTodoActive,
                isNumberListActive = if (!currentState.isBulletListActive) false else currentState.isNumberListActive
            )
        }
//        applyParagraphFormat(RichTextToolType.BULLET_LIST)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.BulletedListToggled(_toolBarState.value.isBulletListActive),
            _toolBarState.value
        )
    }
    
    /**
     * 处理有序列表切换
     */
    fun handleNumberListToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                isNumberListActive = !currentState.isNumberListActive,
                // 互斥逻辑
                isTodoActive = if (!currentState.isNumberListActive) false else currentState.isTodoActive,
                isBulletListActive = if (!currentState.isNumberListActive) false else currentState.isBulletListActive
            )
        }
//        applyParagraphFormat(RichTextToolType.NUMBER_LIST)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.NumberedListToggled(_toolBarState.value.isNumberListActive),
            _toolBarState.value
        )
    }
    
    // ==================== 文本格式处理 ====================
    
    /**
     * 处理粗体切换
     */
    fun handleBoldToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(isBoldActive = !currentState.isBoldActive)
        }
        triggerStyleActionEvent(
            RichTextStyleActionEvent.BoldToggled(_toolBarState.value.isBoldActive),
            _toolBarState.value
        )
    }
    
    /**
     * 处理斜体切换
     */
    fun handleItalicToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(isItalicActive = !currentState.isItalicActive)
        }
        triggerStyleActionEvent(
            RichTextStyleActionEvent.ItalicToggled(_toolBarState.value.isItalicActive),
            _toolBarState.value
        )
    }
    
    /**
     * 处理下划线切换
     */
    fun handleUnderlineToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(isUnderlineActive = !currentState.isUnderlineActive)
        }
        triggerStyleActionEvent(
            RichTextStyleActionEvent.UnderlineToggled(_toolBarState.value.isUnderlineActive),
            _toolBarState.value
        )
    }
    
    /**
     * 处理删除线切换
     */
    private fun handleStrikethroughToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(isStrikethroughActive = !currentState.isStrikethroughActive)
        }
        triggerStyleActionEvent(
            RichTextStyleActionEvent.StrikethroughToggled(_toolBarState.value.isStrikethroughActive),
            _toolBarState.value
        )
    }
    
    // ==================== 颜色处理 ====================

    // 添加防重复执行的标志
    private var isApplyingTextColor = false
    
    /**
     * 处理文本颜色变化
     */
    private fun handleTextColorChange(color: Color, item: RichTextToolBarItem) {
        // 防止重复执行
        if (isApplyingTextColor) {
            return
        }

        // 检查颜色是否真的改变了
        val currentColor = _toolBarState.value.textColor
        if (currentColor == color) {
            return
        }

        isApplyingTextColor = true

        _toolBarState.update { currentState ->
            currentState.copy(
                textColor = color,
                showTextColorPicker = false
            )
        }
//        applyColorFormat(RichTextToolType.TEXT_COLOR, color)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.FontColorApplied(color.toArgb()),
            _toolBarState.value
        )

        // 延迟重置标志，避免阻止正常的后续操作
        viewModelScope.launch {
            delay(100)
            isApplyingTextColor = false
        }
    }
    
    /**
     * 处理文本背景颜色变化
     */
    private fun handleTextBgColorChange(color: Color, item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                textBgColor = color,
                showTextBgColorPicker = false
            )
        }
//        applyColorFormat(RichTextToolType.TEXT_BG_COLOR, color)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.BackgroundColorApplied(color.toArgb()),
            _toolBarState.value
        )
    }
    
    /**
     * 处理文本颜色选择器切换
     */
    private fun handleTextColorToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                showTextColorPicker = !currentState.showTextColorPicker,
                // 关闭其他选择器
                showTextBgColorPicker = false,
                showFontSizeDropdown = false
            )
        }
    }
    
    /**
     * 处理文本背景颜色选择器切换
     */
    private fun handleTextBgColorToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                showTextBgColorPicker = !currentState.showTextBgColorPicker,
                // 关闭其他选择器
                showTextColorPicker = false,
                showFontSizeDropdown = false
            )
        }
    }
    
    // ==================== 字体大小处理 ====================
    
    /**
     * 处理字体大小变化
     */
    private fun handleFontSizeChange(size: Int, item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                selectedFontSize = size,
                showFontSizeDropdown = false
            )
        }
//        applyFontSize(size)
        triggerStyleActionEvent(RichTextStyleActionEvent.FontSizeApplied(size), _toolBarState.value)
    }
    
    /**
     * 处理字体大小选择器切换
     */
    private fun handleFontSizeToggle(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(
                showFontSizeDropdown = !currentState.showFontSizeDropdown,
                // 关闭其他选择器
                showTextColorPicker = false,
                showTextBgColorPicker = false
            )
        }
    }
    
    // ==================== 对齐方式处理 ====================
    
    /**
     * 处理左对齐
     */
    private fun handleAlignLeftClick(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(alignmentState = Layout.Alignment.ALIGN_NORMAL)
        }
//        applyAlignment(AlignmentState.LEFT)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.AlignmentApplied(_toolBarState.value.alignmentState),
            _toolBarState.value
        )
    }
    
    /**
     * 处理居中对齐
     */
    private fun handleAlignCenterClick(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(alignmentState = Layout.Alignment.ALIGN_CENTER)
        }
//        applyAlignment(AlignmentState.CENTER)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.AlignmentApplied(_toolBarState.value.alignmentState),
            _toolBarState.value
        )
    }
    
    /**
     * 处理右对齐
     */
    private fun handleAlignRightClick(item: RichTextToolBarItem) {
        _toolBarState.update { currentState ->
            currentState.copy(alignmentState = Layout.Alignment.ALIGN_OPPOSITE)
        }
//        applyAlignment(AlignmentState.RIGHT)
        triggerStyleActionEvent(
            RichTextStyleActionEvent.AlignmentApplied(_toolBarState.value.alignmentState),
            _toolBarState.value
        )
    }
    
    // ==================== 缩进处理 ====================
    
    /**
     * 处理减少缩进
     */
    private fun handleIndentDecrease(item: RichTextToolBarItem) {
        triggerStyleActionEvent(RichTextStyleActionEvent.IndentLeftApplied, _toolBarState.value)
    }
    
    /**
     * 处理增加缩进
     */
    private fun handleIndentIncrease(item: RichTextToolBarItem) {
        triggerStyleActionEvent(RichTextStyleActionEvent.IndentRightApplied, _toolBarState.value)
    }
    
    // ==================== 公共方法 ====================
    
    /**
     * 关闭所有下拉菜单
     */
    fun dismissAllDropdowns() {
        _toolBarState.update { currentState ->
            currentState.copy(
                showFontSizeDropdown = false,
                showTextColorPicker = false,
                showTextBgColorPicker = false
            )
        }
    }
    
    /**
     * 更新工具栏配置
     */
    fun updateConfig(newConfig: RichTextToolBarConfig) {
        _toolBarConfig.value = newConfig
    }
    
    /**
     * 重置工具栏状态
     */
    fun resetState() {
//        _toolBarState.value = RichTextToolBarState()
    }
    
    /**
     * 启用/禁用工具栏
     */
    fun setEnabled(enabled: Boolean) {
        _toolBarState.update { currentState ->
            currentState.copy(isEnabled = enabled)
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        ComposableToast.show(GlobalContext.instance, message)
    }
    
    /**
     * 创建字体大小列表，1-64范围，20以上的字号间隔为2
     */
    private fun createFontSizeList(): List<Int> {
        val smallSizes = (1..20).toList() // 1-20的字号，间隔为1
        val largeSizes = (22..64 step 2).toList() // 22-64的字号，间隔为2
        return smallSizes + largeSizes
    }

    fun startNavigation() {

    }

    fun operateBottomAIPop(isShow: Boolean=false) {
        _isShowBottomAIPop.value = isShow
    }

    companion object {
        private const val TAG = "RichTextToolBarViewModel"
    }
}
