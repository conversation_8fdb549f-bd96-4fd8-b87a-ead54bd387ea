package com.tcl.ai.note.handwritingtext.richtext.styles;

import static java.lang.Math.min;

import android.text.Editable;
import android.text.Spannable;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextParagraphUtil;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

/**
 * All Rights Reserved.
 *
 * <AUTHOR> Liu
 */
public class ARE_ListNumber implements IARE_Style {

    private ImageView mListNumberImageView;


    private boolean toMergeForward = false;

    private boolean isListNumberCheck = false;
    private boolean isListNumberValid = false;

    private AREditText mEditText;

    public static final Class<?> SPAN_CLASS = ARE_ListNumber.class;

    public ARE_ListNumber() {
        super();
    }

    @Override
    public boolean getIsChecked() {
        return isListNumberCheck;
    }
    @Override
    public void setChecked(boolean isChecked) { isListNumberCheck = isChecked; }
    @Override
    public void setEditText(AREditText editText) {
        this.mEditText = editText;
    }
    @Override
    public EditText getEditText() {
        return this.mEditText;
    }
    @Override
    public ImageView getImageView() {
        return mListNumberImageView;
    }

    @Override
    public void setisValid(boolean isValid) {
        isListNumberValid = isValid;
    }

    @Override
    public boolean getIsValid() {
        return isListNumberValid;
    }

    @Override
    public void updateCheckStatus(boolean checked) {
        setChecked(checked);
    }

    @Override
    public void setListenerForImageView(final ImageView imageView) {
        imageView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
    }

    public void setListNumber() {
        if (!isListNumberValid) {
            return;
        }
        Logger.d("datahub, number_click");
        mEditText.stopUiSHowMonitor();
        isListNumberCheck = !isListNumberCheck;
        mEditText.updateParagraphCheckStatus(ARE_ListNumber.this, isListNumberCheck);
        mEditText.stopApplyMonitor();
        mEditText.stopStorageMonitor();
        mEditText.restoreDefaultLineSpace();
        Logger.d("yjh", "ARE_ListNumber onClick, spanSize=" + mEditText.getEditableText().getSpans(0,mEditText.getEditableText().length(),Object.class).length);
        int currentLine = Util.getCurrentCursorLine(mEditText);
        int start = Util.getThisLineStart(mEditText, currentLine);
        int end = Util.getThisLineEnd(mEditText, currentLine);

        Logger.d("ListNumber","currentLine=" + currentLine + " start=" + start + " end=" + end);
        Editable editable = mEditText.getText();

        //
        // Check if there is any ListNumberSpan first.
        // If there is ListNumberSpan, it means this case:
        // User has typed in:
        //
        // * aa
        // * bb
        // * cc
        //
        // Then user clicks the Number icon at 1 or 2 or any other item
        // He wants to change current ListBulletSpan to ListNumberSpan
        //
        // So it becomes:
        // For example: user clicks Number icon at 2:
        // * aa
        // 1. bb
        // * cc

        int selectionStart = mEditText.getSelectionStart();
        int selectionEnd = mEditText.getSelectionEnd();
        int selectionStartLine = Util.getLineBySelection(mEditText, selectionStart);
        int selectionEndLine = Util.getLineBySelection(mEditText, selectionEnd);

        // -----case 有多行同时选中场景
        try {
            if (selectionStart != -1 && selectionEnd != -1 && selectionStartLine < selectionEndLine) {
                if(isListNumberCheck){
                    RichTextParagraphUtil.setParagraphSpanByLine(mEditText, editable, selectionStart, selectionEnd, ListNumberSpan.class);
                } else {
                    RichTextParagraphUtil.clearParagraphSpansByLine(mEditText, editable, selectionStart, selectionEnd);
                }
                triggerTextChange();
                return;
            }

        } catch (Exception e) {
            Logger.d("ARE_ListNumber", "setListNumber error: " + e);
        }

        // -----case 有多行同时选中场景


        // 处理单行逻辑
        ListBulletSpan[] listBulletSpans = editable.getSpans(selectionStart,
                selectionEnd, ListBulletSpan.class);
        UpcomingListSpan[] upcomingListSpans = editable.getSpans(selectionStart,
                selectionEnd,UpcomingListSpan.class);

        //if current line span is bullet, just change to listNumber and then return
        if (null != listBulletSpans && listBulletSpans.length > 0) {
            changeListBulletSpanToListNumberSpan(editable, listBulletSpans);
            // Change the content to trigger the editable redraw
            triggerTextChange();
            Logger.d("yjh", "ARE_ListNumber change onClick, spanSize=" + mEditText.getEditableText().getSpans(0,mEditText.getEditableText().length(),Object.class).length);
            return;
        }else if(null != upcomingListSpans && upcomingListSpans.length > 0) {
            changeUpcomingToListNumberSpan(editable,upcomingListSpans);
            // Change the content to trigger the editable redraw
            triggerTextChange();
            return;
        }

        try {
            // -----case 有多行同时选中场景
            if (selectionStart != -1 && selectionEnd != -1 && selectionStartLine < selectionEndLine) {
                makeOrUnMarkMultiLineSpan(editable, selectionStart, selectionEnd);
                triggerTextChange();
                return;
            }
        } catch (Exception e) {
            Logger.d("ARE_ListNumber", "setListNumber error: " + e);
        }
        // -----case 有多行同时选中场景

        ListNumberSpan[] listNumberSpans = editable.getSpans(start, end,
                ListNumberSpan.class);

        //if current line span isn't listNumber or bullet, just make a new one of listNumber
        if (null == listNumberSpans || listNumberSpans.length == 0) {
            //
            // Current line is not list item span
            // By clicking the image view, we should make it as
            // ListItemSpan
            // And ReOrder
            //
            // ------------ CASE 1 ------------------
            // Case 1:
            // Nothing types in, user just clicks the List image
            // For this case we need to mark it as ListItemSpan

            //
            // ------------ CASE 2 ------------------
            // Case 2:
            // Before or after the current line, there are already
            // ListItemSpan have been made
            // Like:
            // 1. AAA
            // BBB
            // 1. CCC
            //
            // User puts cursor to the 2nd line: BBB
            // And clicks the List image
            // For this case we need to make current line as
            // ListItemSpan
            // And, we should also reOrder them as:
            //
            // 1. AAA
            // 2. BBB
            // 3. CCC
            //

            // if (end > 0) {} // #End of if (end > 0)

            //
            // Case 2
            //
            // There are list item spans ahead current editing
            int thisNumber = 1;
            // 修复：查找前面所有的列表项，而不是只查找很小的范围
            ListNumberSpan[] aheadListItemSpans = null;
            if (start > 0) {
                // 查找从文档开始到当前位置之前的所有列表项
                aheadListItemSpans = editable.getSpans(0, start - 1, ListNumberSpan.class);
                Logger.d("ARE_ListNumber", "Looking for ahead spans in range [0, " + (start - 1) + "], found " +
                        (aheadListItemSpans != null ? aheadListItemSpans.length : 0) + " spans");
            }

            ListNumberSpan currentSpan = null;
            if (null != aheadListItemSpans && aheadListItemSpans.length > 0) {
                // 找到最接近当前位置的列表项（位置最靠后的）
                ListNumberSpan previousListItemSpan = null;
                int maxSpanEnd = -1;

                for (ListNumberSpan span : aheadListItemSpans) {
                    int spanEnd = editable.getSpanEnd(span);
                    if (spanEnd > maxSpanEnd && spanEnd < start) {
                        maxSpanEnd = spanEnd;
                        previousListItemSpan = span;
                    }
                }

                Logger.d("ARE_ListNumber", "Found previous list item span with end position " + maxSpanEnd +
                        ", number=" + (previousListItemSpan != null ? previousListItemSpan.getNumber() : "null"));

                // 修复：检查前一个列表项与当前位置之间是否连续（没有空白行分隔）
                if (previousListItemSpan != null) {
                    int previousSpanEnd = editable.getSpanEnd(previousListItemSpan);
                    boolean isConsecutive = areSpansConsecutiveInternal(editable, previousSpanEnd, start);

                    if (!isConsecutive) {
                        // 如果不连续（被空白行或其他内容分隔），则不继续前面的编号
                        Logger.d("ARE_ListNumber", "Previous list item is not consecutive (separated by blank lines or other content), starting from 1");
                        previousListItemSpan = null;
                    } else {
                        Logger.d("ARE_ListNumber", "Previous list item is consecutive, continuing numbering from " + previousListItemSpan.getNumber());
                    }
                }
                if (null != previousListItemSpan) {
                    int pStart = editable
                            .getSpanStart(previousListItemSpan);
                    int pEnd = editable
                            .getSpanEnd(previousListItemSpan);
                    //
                    // Handle this case:
                    // 1. A
                    // B
                    // C
                    // 1. D
                    //
                    // User puts focus to B and click List icon, to
                    // change it to:
                    // 2. B
                    //
                    // Then user puts focus to C and click List icon, to
                    // change it to:
                    // 3. C
                    // For this one, we need to finish the span "2. B"
                    // correctly
                    // Which means we need to set the span end to a
                    // correct value
                    // This is doing this.
                    if(pEnd == 0){
                        editable.removeSpan(previousListItemSpan);
                    }else if (editable.charAt(pEnd - 1) == Constants.CHAR_NEW_LINE) {
                        editable.removeSpan(previousListItemSpan);
                        editable.setSpan(previousListItemSpan, pStart,
                                pEnd - 1,
                                Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    }

                    int previousNumber = previousListItemSpan
                            .getNumber();
                    thisNumber = previousNumber + 1;
                    currentSpan = makeLineAsList(thisNumber);
                    Logger.d("ARE_ListNumber", "onClick: Continuing numbering from previous span, thisNumber=" + thisNumber);
                } else {
                    // 没有前置列表项或前置列表项不连续，从1开始
                    thisNumber = 1;
                    currentSpan = makeLineAsList(1);
                    Logger.d("ARE_ListNumber", "onClick: No consecutive previous span, starting from 1");
                }
            } else {
                //
                // Case 1: 没有前置列表项
                // 直接从1开始
                thisNumber = 1;
                currentSpan = makeLineAsList(1);
                Logger.d("ARE_ListNumber", "onClick: No previous spans found, starting from 1");
            }

            //
            // Case 2
            //
            // Handle behind list item spans
            // reorder them
            // int totalLength = editable.toString().length();
            // if (totalLength > end) {}
            int spanEnd = editable.getSpanEnd(currentSpan);
            // 修复：使用spanEnd + 1确保不会重新编号刚创建的span
            reNumberBehindListItemSpans(spanEnd + 1, editable, thisNumber);
            Logger.d("ARE_ListNumber", "Created new list item with number " + thisNumber +
                    ", renumbering spans after position " + (spanEnd + 1));
        }
        //if current line span is ListNumber, just remove the span
        else {
            Logger.d("ARE_ListNumber onClick remove");
            //
            // Current line is list item span
            // By clicking the image view, we should remove the
            // ListItemSpan
            ListNumberSpan currentLineListItemSpan = listNumberSpans[0];
            int spanEnd = editable.getSpanEnd(currentLineListItemSpan);
            int spanStart = editable.getSpanStart(currentLineListItemSpan);

            Logger.d("onClick, span start=" + spanStart + " end=" + spanEnd);

            editable.removeSpan(currentLineListItemSpan);

            //
            // Change the content to trigger the editable redraw
            //editable.insert(spanEnd, Constants.ZERO_WIDTH_SPACE_STR);
            if(editable.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT)
                editable.delete(spanStart, spanStart + 1);

            //
            // The new list should start from 1
            reNumberBehindListItemSpans(spanEnd, editable, 0);
            updateCheckStatus(false);
        }

        // Change the content to trigger the editable redraw
        triggerTextChange();
        Logger.d("yjh", "ARE_ListNumber over onClick, spanSize=" + mEditText.getEditableText().getSpans(0,mEditText.getEditableText().length(),Object.class).length);
    }

    private void makeOrUnMarkMultiLineSpan(Editable editable, int selectionStart, int selectionEnd) {
        int start = TextUtils.lastIndexOf(editable, '\n', selectionStart) + 1;
        int end = TextUtils.indexOf(editable, '\n', selectionEnd);
        if (end == -1) {
            end = editable.length() - 1;
        }

        int number = 1;

        //取前一个ListNumberSpan的序号
        if (start > 0) {
            ListNumberSpan[] prevSpans = editable.getSpans(start - 2, start - 1, ListNumberSpan.class);
            if (prevSpans.length > 0 && prevSpans[prevSpans.length - 1] != null) {
                number = prevSpans[prevSpans.length - 1].getNumber() + 1;
            }
        }

        ArrayList<ListNumberSpan> existedSpanList = new ArrayList<>();
        boolean setNewSpan = false;//用来判断是否需要移除所有Span

        while (start <= end) {
            ListNumberSpan span = new ListNumberSpan(number);
            int nextStart;
            if ((nextStart = TextUtils.indexOf(editable, '\n', start)) != -1) {//寻找下一行的位置
                ListNumberSpan[] currentLineSpans = editable.getSpans(start, nextStart, ListNumberSpan.class);
                if (start == nextStart) {//单独的换行符插入一个零宽字符占位，保证能保存到HTML里面
                    editable.insert(nextStart++, Constants.ZERO_WIDTH_SPACE_STR);
                    end++;
                }
                if (currentLineSpans == null || currentLineSpans.length == 0) {//当前行不存在Span, 增加
                    span.setNumber(number++);
                    editable.setSpan(span, start, nextStart, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    setNewSpan = true;
                } else {//当前行已有这个Span，修改原来Span的序号
                    for (ListNumberSpan it : currentLineSpans) {
                        it.setNumber(number++);
                    }
                    Collections.addAll(existedSpanList, currentLineSpans);
                }
            } else {//reach end
                ListNumberSpan[] existSpans = editable.getSpans(start, end, ListNumberSpan.class);
                if (existSpans == null || existSpans.length == 0) {
                    span.setNumber(number);
                    editable.setSpan(span, start, editable.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    setNewSpan = true;
                } else {//
                    Collections.addAll(existedSpanList, existSpans);
                    //修改原来Span的序号
                    for (ListNumberSpan it : existSpans) {
                        it.setNumber(number++);
                    }
                }
                break;
            }
            start = nextStart + 1;
        }

        //所有的Span都有需要，一处所有
        if (!setNewSpan) {
            for (ListNumberSpan span : existedSpanList) {
                editable.removeSpan(span);
            }
        }

        //遍历后续的ListNumberSpan，连续或者从1递增
        if (setNewSpan) {
            reNumberBehindListItemSpans(end + 1, editable, number - 1);
        } else {//前面的Span已经全部取消
            reNumberBehindListItemSpans(end + 1, editable, 0);
        }

    }

    public void logSE(){
        int currentLine = Util.getCurrentCursorLine(mEditText);
        int start = Util.getThisLineStart(mEditText, currentLine);
        int end = Util.getThisLineEnd(mEditText, currentLine);

        int selectionStart = mEditText.getSelectionStart();
        int selectionEnd = mEditText.getSelectionEnd();

        Logger.d("logSE, line start=" + start + " end=" + end);
        Logger.d("logSE, select start=" + selectionStart + " end=" + selectionEnd);



    }

    public void triggerTextChange(){
        int selectionStart = mEditText.getSelectionStart();
        int selectionEnd = mEditText.getSelectionEnd();

        Editable editable = mEditText.getText();
        Logger.d("triggerTextChange, select start=" + selectionStart + " end=" + selectionEnd);
        editable.insert(selectionEnd, Constants.ZERO_WIDTH_SPACE_STR);
        Logger.d("triggerTextChange, mid!!");
        mEditText.startStorageMonitor();
        editable.delete(selectionEnd, min(editable.length(), selectionEnd + 1));
        mEditText.startApplyMonitor();
        mEditText.startUiShowMonitor();
        Logger.d("triggerTextChange, over!!");
        RichTextKTUtilsKt.recordApplyStyleToUndoRedo(selectionStart, selectionEnd, getIsChecked(), mEditText, ARE_ListNumber.SPAN_CLASS);
    }

    @Override
    public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
        // logAllListItems(editable, true);
        ListNumberSpan[] listSpans = editable.getSpans(start, end,
                ListNumberSpan.class);

        if (null == listSpans || listSpans.length == 0) {
            // 如果是删除操作且没有找到列表项，则重新编号所有剩余的列表项
            if (end <= start) {
                reNumberAllListItems(editable);
            }
            return;
        }

        Logger.d("listNumberSpan applyStyle start/end=" + start + "/" + end + " length=" + listSpans.length);

        if (end > start) {
            //
            // User inputs
            //
            // To handle the \n case

            // int totalLen = editable.toString().length();
            // Util.log("ListNumber - total len == " + totalLen);
            char c = editable.charAt(end - 1);
            if (c == Constants.CHAR_NEW_LINE) {
                int listSpanSize = listSpans.length;
                int previousListSpanIndex = listSpanSize - 1;
                if (previousListSpanIndex > -1) {
                    ListNumberSpan previousListSpan = listSpans[previousListSpanIndex];
                    int lastListItemSpanStartPos = editable
                            .getSpanStart(previousListSpan);
                    int lastListItemSpanEndPos = editable
                            .getSpanEnd(previousListSpan);
                    CharSequence listItemSpanContent = editable.subSequence(
                            lastListItemSpanStartPos, lastListItemSpanEndPos);

                    if (isEmptyListItemSpan(listItemSpanContent)) {
                        //
                        // Handle this case:
                        // 1. A
                        // 2. <User types \n here, at an empty span>
                        //
                        // The 2 chars are:
                        // 1. ZERO_WIDTH_SPACE_STR
                        // 2. \n
                        //
                        // We need to remove current span and do not re-create
                        // span.
                        editable.removeSpan(previousListSpan);

                        //
                        // Deletes the ZERO_WIDTH_SPACE_STR and \n
                        editable.delete(lastListItemSpanStartPos,
                                lastListItemSpanEndPos);

                        //
                        // Restart the counting for the list item spans after
                        // previousListSpan
                        reNumberBehindListItemSpans(lastListItemSpanStartPos,
                                editable, 0);
                        updateCheckStatus(false);
                        mEditText.post(new Runnable() {
                            @Override
                            public void run() {
                                int selStart = mEditText.getSelectionStart();
                                int selEnd = mEditText.getSelectionEnd();
                                mEditText.onSelectionChanged(selStart, selEnd);
                            }
                        });
                        return;
                    } else {
                        //
                        // Handle this case:
                        //
                        // 1. A
                        // 2. C
                        // 3. D
                        //
                        // User types \n after 'A'
                        // Then
                        // We should see:
                        // 1. A
                        // 2.
                        // 3. C
                        // 4. D
                        //
                        // We need to end the first span
                        // Then start the 2nd span
                        // Then reNumber the following list item spans
                        if (end > lastListItemSpanStartPos) {
                            editable.removeSpan(previousListSpan);
                            editable.setSpan(previousListSpan,
                                    lastListItemSpanStartPos, end - 1,
                                    Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        }
                    }
                    int lastListItemNumber = previousListSpan.getNumber();
                    int thisNumber = lastListItemNumber + 1;
                    ListNumberSpan newListItemSpan = makeLineAsList(thisNumber);
                    end = editable.getSpanEnd(newListItemSpan);
                    // 修复：使用end + 1确保不会重新编号刚创建的span
                    reNumberBehindListItemSpans(end + 1, editable, thisNumber);
                    Logger.d("ARE_ListNumber", "applyStyle: Created new list item with number " + thisNumber +
                            ", renumbering spans after position " + (end + 1));
                } // #End of if it is in ListItemSpans..
            } // #End of user types \n
        } else {
            //
            // User deletes
            //
            int spanStart = editable.getSpanStart(listSpans[0]);
            int spanEnd = editable.getSpanEnd(listSpans[0]);
            ListNumberSpan theFirstSpan = listSpans[0];
            if (listSpans.length > 1) {
                int theFirstSpanNumber = theFirstSpan.getNumber();
                for (ListNumberSpan lns : listSpans) {
                    if (lns.getNumber() < theFirstSpanNumber) {
                        theFirstSpan = lns;
                    }
                }
                spanStart = editable.getSpanStart(theFirstSpan);
                spanEnd = editable.getSpanEnd(theFirstSpan);
            }


            Util.log("Delete spanStart = " + spanStart + ", spanEnd = "
                    + spanEnd + " , start == " + start);
            if (spanStart >= spanEnd) {
                Util.log("case 1");
                //
                // User deletes the last char of the span
                // So we think he wants to remove the span
                boolean hasRemovedSpans = false;
                for (ListNumberSpan listSpan : listSpans) {
                    editable.removeSpan(listSpan);
                    hasRemovedSpans = true;
                }

                //
                // To delete the previous span's \n
                // So the focus will go to the end of previous span
                if (spanStart > 0) {
                    editable.delete(spanStart - 1, spanEnd);
                }

                // 如果删除了span，重新编号所有剩余的列表项
                if (hasRemovedSpans) {
                    Logger.d("ARE_ListNumber", "applyStyle case 1: triggering renumbering after removing spans");
                    reNumberAllListItems(editable);
                }
            } else if (start == spanStart) {
                Util.log("case 2");
                //*|XXXX
                boolean hasRemovedSpans = false;
                for (Object what : listSpans) {
                    editable.removeSpan(what);
                    hasRemovedSpans = true;
                }
                updateCheckStatus(false);

                // 检查当前行删除前缀后是否还有内容
                int currentLine = Util.getCurrentCursorLine(mEditText);
                int lineStart = Util.getThisLineStart(mEditText, currentLine);
                int lineEnd = Util.getThisLineEnd(mEditText, currentLine);

                // 检查行内容（排除零宽字符）
                boolean hasRealContent = false;
                if (lineEnd > lineStart) {
                    for (int i = lineStart; i < lineEnd; i++) {
                        if (i < editable.length() && editable.charAt(i) != Constants.ZERO_WIDTH_SPACE_INT && editable.charAt(i) != '\n') {
                            hasRealContent = true;
                            break;
                        }
                    }
                }

                if (hasRealContent) {
                    // 如果当前行还有内容，需要将内容合并到上一行并扩展上一行的span范围

                    // 找到上一行的ListNumberSpan
                    ListNumberSpan previousSpan = null;
                    if (start > 1) {
                        ListNumberSpan[] previousSpans = editable.getSpans(start - 2, start - 1, ListNumberSpan.class);
                        if (previousSpans != null && previousSpans.length > 0) {
                            previousSpan = previousSpans[previousSpans.length - 1];
                        }
                    }

                    // 删除零宽字符
                    if (start > 0 && start < editable.length() && editable.charAt(start) == Constants.ZERO_WIDTH_SPACE_INT) {
                        editable.delete(start, start + 1);
                        start--; // 调整位置
                    }

                    // 删除前面的换行符，让内容合并到上一行
                    if (start > 0 && editable.charAt(start - 1) == '\n') {
                        editable.delete(start - 1, start);
                        start--; // 调整位置
                    }

                    // 如果找到了上一行的span，扩展其范围包含合并过来的内容
                    if (previousSpan != null) {
                        int previousSpanStart = editable.getSpanStart(previousSpan);
                        int previousSpanEnd = editable.getSpanEnd(previousSpan);

                        // 计算新的span结束位置（包含合并过来的内容）
                        int newLineEnd = Util.getThisLineEnd(mEditText, Util.getCurrentCursorLine(mEditText));
                        if (newLineEnd > 0 && newLineEnd <= editable.length() && editable.charAt(newLineEnd - 1) == '\n') {
                            newLineEnd--;
                        }

                        // 重新设置span范围
                        editable.removeSpan(previousSpan);
                        editable.setSpan(previousSpan, previousSpanStart, newLineEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    }
                } else {
                    // 如果当前行没有实际内容，按原逻辑处理
                    if (Util.getCurrentCursorLine(mEditText) != 0) {
                        editable.insert(start, "\n");
                    }
                }

                // 如果删除了span，需要重新编号剩余的列表项
                if (hasRemovedSpans) {
                    Logger.d("ARE_ListNumber", "applyStyle case 2: triggering renumbering after removing spans");
                    reNumberAllListItems(editable);
                }
                return;
            } else if (start == spanEnd) {
                Util.log("case 3");
                //
                // User deletes the first char of the span
                // So we think he wants to remove the span
                if (editable.length() > start) {
                    if (editable.charAt(start) == Constants.CHAR_NEW_LINE) {
                        // The error case to handle
                        Util.log("case 3-1");
                        ListNumberSpan[] spans = editable.getSpans(start, start, ListNumberSpan.class);
                        Util.log(" spans len == " + spans.length);
                        if (spans.length > 0) {
                            Util.log("case 3-1-1");
                            mergeForward(editable, theFirstSpan, spanStart, spanEnd);
                        } else {
                            Util.log("case 3-1-2");
                            editable.removeSpan(spans[0]);
                        }
                    } else {
                        mergeForward(editable, theFirstSpan, spanStart, spanEnd);
                    }
                }
            } else if (start > spanStart && end < spanEnd) {
                Util.log("case 4");
                //
                // Handle this case:
                // 1. AAA1
                // 2. BBB2
                // 3. CCC3
                //
                // User deletes '1' / '2' / '3'
                // Or any other character inside of a span
                //
                // For this case we won't need do anything
                // As we need to keep the span styles as they are
                return;
            } else {
                Util.log("case X");
                if (editable.length() > start) {
                    Util.log("start char == " + (int) editable.charAt(start));
                }
                //
                // Handle this case:
                // 1. A
                // 2. B
                // x
                // 1. C
                // 2. D
                //
                // When user deletes the "x"
                // Then merge two lists, so it should be changed to:
                // 1. A
                // 2. B
                // 3. C
                // 4. D
                //
                // mergeLists();
                int previousNumber = theFirstSpan.getNumber();
                reNumberBehindListItemSpans(end, editable, previousNumber);
            }
        }
    } // # End of applyStyle(..)

    @Override
    public void removeStyle(Editable editable, int start, int end) {
        ListNumberSpan[] listSpans = editable.getSpans(start, end,
                ListNumberSpan.class);

        if (null == listSpans) {
            return;
        }

        boolean hasRemovedSpans = false;
        java.util.List<Integer> zeroWidthPositions = new java.util.ArrayList<>();

        for(ListNumberSpan span : listSpans) {
            // 在删除span之前先获取位置信息
            int spanStart = editable.getSpanStart(span);

            // 记录需要删除的零宽字符位置
            if (spanStart >= 0 && spanStart < editable.length() &&
                    editable.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
                zeroWidthPositions.add(spanStart);
            }

            editable.removeSpan(span);
            hasRemovedSpans = true;
        }

        // 从后往前删除零宽字符，避免位置偏移问题
        java.util.Collections.sort(zeroWidthPositions, java.util.Collections.reverseOrder());
        for (Integer pos : zeroWidthPositions) {
            if (pos < editable.length() && editable.charAt(pos) == Constants.ZERO_WIDTH_SPACE_INT) {
                editable.delete(pos, pos + 1);
            }
        }

        // 如果删除了任何span，重新编号剩余的列表项
        if (hasRemovedSpans) {
            Logger.d("ARE_ListNumber", "removeStyle: triggering renumbering after removing spans");
            reNumberAllListItems(editable);
        }
    }

    @Override
    public void setStyleStatusListener(StyleStatusListener listener) {

    }

    @Override
    public Boolean needApplyStyle() {
        return true;
    }

    protected void mergeForward(Editable editable, ListNumberSpan listSpan, int spanStart, int spanEnd) {
        Logger.d("number","merge forward 1");
        if (editable.length() < spanEnd + 1) {
            return;
        }
        Logger.d("number","merge forward 2");
        ListNumberSpan[] targetSpans = editable.getSpans(spanEnd, spanEnd + 1, ListNumberSpan.class);
        // logAllListItems(editable, false);
        if (targetSpans == null || targetSpans.length == 0) {
            Logger.d("ARE_ListNumber", "mergeForward: no target spans, triggering full renumbering");
            int mergeEnd = spanEnd;
            while (mergeEnd < editable.length() && editable.charAt(mergeEnd) != '\n') {
                mergeEnd++;
            }
            if (mergeEnd > spanEnd) {
                // 有可以合并的普通文本，将listSpan范围扩展
                editable.removeSpan(listSpan);
                editable.setSpan(listSpan, spanStart, mergeEnd, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                Logger.d("ARE_ListNumber", "mergeForward: expanded span to include ordinary text, new end: " + mergeEnd);
            }
            // 仍然全局重算（这样连续会被编号为4、5、...）
            reNumberAllListItems(editable);
            return;
        }

        for(int s=spanEnd; s<editable.length(); s++){
//            Logger.d("for s=" + s + " c=" + editable.charAt(s));
            if(editable.charAt(s) == Constants.ZERO_WIDTH_SPACE_INT) {
                mEditText.stopAllMonitor();
                editable.delete(s, s + 1);
                mEditText.startAllMonitor();
                targetSpans = editable.getSpans(
                        spanEnd, spanEnd + 1, ListNumberSpan.class);
                if (targetSpans == null || targetSpans.length == 0) {
                    Logger.d("ARE_ListNumber", "mergeForward: no target spans after zero-width deletion, triggering full renumbering");
                    reNumberAllListItems(editable);
                    return;
                }
            }
        }

        ListNumberSpan firstTargetSpan = targetSpans[0];
        ListNumberSpan lastTargetSpan = targetSpans[0];

        if (targetSpans.length > 0) {
            int firstTargetSpanNumber = firstTargetSpan.getNumber();
            int lastTargetSpanNumber = lastTargetSpan.getNumber();
            for (ListNumberSpan lns : targetSpans) {
                int lnsNumber = lns.getNumber();
                if (lnsNumber < firstTargetSpanNumber) {
                    firstTargetSpan = lns;
                    firstTargetSpanNumber = lnsNumber;
                }
                if (lnsNumber > lastTargetSpanNumber) {
                    lastTargetSpan = lns;
                    lastTargetSpanNumber = lnsNumber;
                }
            }
        }
        int targetStart = editable.getSpanStart(firstTargetSpan);
        int targetEnd = editable.getSpanEnd(lastTargetSpan);

        // 检查目标span是否仍然有效
        if (targetStart < 0 || targetEnd < 0 || targetStart > targetEnd) {
            Logger.d("number","merge forward failed: target spans are invalid");
            Logger.d("ARE_ListNumber", "mergeForward: invalid target spans, triggering full renumbering");
            reNumberAllListItems(editable);
            return;
        }

        Logger.d("number","merge to remove span start == " + targetStart + ", target end = " + targetEnd + ", target number = " + firstTargetSpan.getNumber());

        int targetLength = targetEnd - targetStart;
        spanEnd = spanEnd + targetLength;

        // 检查最终的span范围是否有效
        if (spanStart < 0 || spanEnd < 0 || spanStart > spanEnd || spanEnd > editable.length()) {
            Logger.d("number","merge forward failed: final span range is invalid");
            Logger.d("ARE_ListNumber", "mergeForward: invalid final span range, triggering full renumbering");
            reNumberAllListItems(editable);
            return;
        }

        boolean hasRemovedSpans = false;
        for (ListNumberSpan targetSpan : targetSpans) {
            editable.removeSpan(targetSpan);
            hasRemovedSpans = true;
        }
        ListNumberSpan[] compositeSpans = editable.getSpans(spanStart, spanEnd, ListNumberSpan.class);
        for (ListNumberSpan lns : compositeSpans) {
            editable.removeSpan(lns);
            hasRemovedSpans = true;
        }
        editable.setSpan(listSpan, spanStart, spanEnd,
                Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        Logger.d("number","merge span start == " + spanStart + " end == " + spanEnd);

        // 如果删除了任何span，重新编号所有列表项
        if (hasRemovedSpans) {
            Logger.d("ARE_ListNumber", "mergeForward: triggering renumbering after removing spans");
            reNumberAllListItems(editable);
        } else {
            reNumberBehindListItemSpans(spanEnd, editable, listSpan.getNumber());
        }
    }

    private void logAllListItems(Editable editable, boolean printDetail) {
        ListNumberSpan[] listItemSpans = editable.getSpans(0,
                editable.length(), ListNumberSpan.class);
        for (ListNumberSpan span : listItemSpans) {
            int ss = editable.getSpanStart(span);
            int se = editable.getSpanEnd(span);
            int flag = editable.getSpanFlags(span);
            Util.log("List All: " + span.getNumber() + " :: start == " + ss
                    + ", end == " + se + ", flag == " + flag);
            if (printDetail) {
                for (int i = ss; i < se; i++) {
                    Util.log("char at " + i + " = " + editable.charAt(i) + " int = " + ((int) (editable.charAt(i))));
                }

                if (editable.length() > se) {
                    Util.log("char at " + se + " = " + editable.charAt(se)+ " int = " + ((int) (editable.charAt(se))));
                }
            }
        }
    }

    /**
     * Check if this is an empty span.
     * <p>
     * <B>OLD COMMENT: and whether it is at the end of the spans list</B>
     *
     * @param listItemSpanContent
     * @return
     */
    private boolean isEmptyListItemSpan(CharSequence listItemSpanContent) {
        int spanLen = listItemSpanContent.length();
        if (spanLen == 1 && listItemSpanContent.charAt(0) == Constants.CHAR_NEW_LINE) {
            return true;
        } else if (spanLen == 2
                && listItemSpanContent.charAt(0) == Constants.ZERO_WIDTH_SPACE_INT
                && listItemSpanContent.charAt(1) == Constants.CHAR_NEW_LINE) {
            //
            // This case:
            // 1. A
            // 2.
            //
            // Line 2 is empty
            return true;
        } else {
            return false;
        }
    }

    private void adjustText(ListNumberSpan listItemSpan){
        String text = mEditText.getText().toString();
        float textWidth = mEditText.getPaint().measureText(mEditText.getText().toString());
        float limitWidth = mEditText.getLayout().getWidth() - listItemSpan.getLeadingMargin(true) - mEditText.getTextSize();

        Logger.d("adjustText textWidth=" + textWidth + " viewWidth=" + mEditText.getWidth() + " layoutWidth=" + mEditText.getLayout().getWidth()
                + " ListNumber margin=" + listItemSpan.getLeadingMargin(true) + " textSize=" + mEditText.getTextSize());

        Logger.d("adjustText lineCount=" + mEditText.getLayout().getLineCount() + " line0Width=" + mEditText.getLayout().getLineWidth(0));

        if(textWidth>limitWidth){

        }

    }

    private void logString(int number, String text){
        for(int i=0; i<text.length() ; i++){
            //Logger.d("char a=" + a + " offset=" + offset);
            Logger.d(number + " char=" + text.charAt(i) + "---i=" + i );
        }
    }

    /**
     * Make the current line a list item.
     *
     * @param num
     * @return
     */
    private ListNumberSpan makeLineAsList(int num) {
        Logger.d("makeLineAsList num=" + num);
        ListNumberSpan listItemSpan = new ListNumberSpan(num);

        int currentLine = Util.getCurrentCursorLine(mEditText);
        int start = Util.getThisLineStart(mEditText, currentLine);
        int end = Util.getThisLineEnd(mEditText, currentLine);
        Editable editable = mEditText.getText();

        boolean wasEmptyLine = !(end > start);

        // 分析行内容
        StringBuilder lineContent = new StringBuilder();
        for (int i = start; i < end && i < editable.length(); i++) {
            char c = editable.charAt(i);
            if (c == Constants.ZERO_WIDTH_SPACE_INT) {
                lineContent.append("[ZWS]");
            } else if (c == '\n') {
                lineContent.append("\\n");
            } else {
                lineContent.append(c);
            }
        }

        Logger.d("ARE_ListNumber", "makeLineAsList: line=" + currentLine + ", start=" + start + ", end=" + end + ", wasEmptyLine=" + wasEmptyLine + ", lineContent='" + lineContent.toString() + "'");

        if(wasEmptyLine) {
            editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
            Logger.d("ARE_ListNumber", "makeLineAsList: inserted zero-width char at position " + start);
        } else {
            // 检查行首是否已有零宽字符，如果没有则插入（参考待办事项的逻辑）
            if (start < editable.length() && editable.charAt(start) != Constants.ZERO_WIDTH_SPACE_INT) {
                editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
                Logger.d("ARE_ListNumber", "makeLineAsList: inserted zero-width char at position " + start + " for non-empty line");
            } else {
                Logger.d("ARE_ListNumber", "makeLineAsList: zero-width char already exists at position " + start);
            }
        }

        start = Util.getThisLineStart(mEditText, currentLine);
        end = Util.getThisLineEnd(mEditText, currentLine);

        if (end > 0 && editable.charAt(end - 1) == Constants.CHAR_NEW_LINE) {
            end--;
        }

        // 边界保护
        if (start < 0) start = 0;
        if (end > editable.length()) end = editable.length();
        if (end < start) {
            Logger.w("ARE_ListNumber", "makeLineAsList: invalid range start=" + start + ", end=" + end + ", force adjusting.");
            end = start;
        }

        editable.setSpan(listItemSpan, start, end,
                Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        // 修正光标位置：无论是空行还是非空行，只要插入了零宽字符就需要修正
        mEditText.post(new Runnable() {
            @Override
            public void run() {
                int currentCursor = mEditText.getSelectionStart();
                int lineStart = Util.getThisLineStart(mEditText, Util.getCurrentCursorLine(mEditText));
                // 如果光标在行首的零宽字符位置，移动到零宽字符之后
                if (currentCursor == lineStart && lineStart < editable.length() &&
                        editable.charAt(lineStart) == Constants.ZERO_WIDTH_SPACE_INT) {
                    mEditText.setSelection(lineStart + 1);
                    Logger.d("ARE_ListNumber", "makeLineAsList: cursor moved from " + currentCursor + " to " + (lineStart + 1));
                } else {
                    Logger.d("ARE_ListNumber", "makeLineAsList: no cursor fix needed, cursor=" + currentCursor + ", lineStart=" + lineStart);
                }
            }
        });

        return listItemSpan;
    }

    /**
     * @param end
     * @param editable
     * @param thisNumber
     */
    /**
     * 重新编号整个文档中的所有列表项
     * 用于删除操作后的全局重新编号
     * 将列表项按连续段分组，每段独立编号
     *
     * @param editable 可编辑文本
     */
    private void reNumberAllListItems(Editable editable) {
        boolean hasChanges = reNumberAllListItemsNoRefresh(editable);

        // 如果有变化，触发UI刷新
        if (hasChanges && mEditText != null) {
            // 使用triggerTextChange方法来触发UI刷新
            mEditText.post(new Runnable() {
                @Override
                public void run() {
                    triggerTextChange();
                    Logger.d("ARE_ListNumber", "Triggered UI refresh after renumbering using triggerTextChange");
                }
            });
        }
    }

    /**
     * @param end
     * @param editable
     * @param thisNumber
     */
    /**
     * 重新编号整个文档中的所有列表项
     * 用于删除操作后的全局重新编号
     * 将列表项按连续段分组，每段独立编号
     *
     * @param editable 可编辑文本
     */
    private boolean reNumberAllListItemsNoRefresh(Editable editable) {
        // 获取文档中所有的ListNumberSpan
        ListNumberSpan[] allSpans = editable.getSpans(0, editable.length(), ListNumberSpan.class);

        if (null == allSpans || allSpans.length == 0) {
            return false;
        }

        // 按照span在文档中的位置排序
        Arrays.sort(allSpans, (span1, span2) -> {
            int start1 = editable.getSpanStart(span1);
            int start2 = editable.getSpanStart(span2);
            return Integer.compare(start1, start2);
        });

        // 将列表项按连续段分组
        java.util.List<java.util.List<ListNumberSpan>> listSegments = groupListSpansIntoSegments(editable, allSpans);

        boolean hasChanges = false;
        int totalProcessed = 0;

        // 对每个连续段独立编号
        for (java.util.List<ListNumberSpan> segment : listSegments) {
            int currentNumber = 1;
            for (ListNumberSpan listItemSpan : segment) {
                int oldNumber = listItemSpan.getNumber();
                if (oldNumber != currentNumber) {
                    Util.log("Renumber segment: Change old number == " + oldNumber + " to new number == " + currentNumber);
                    listItemSpan.setNumber(currentNumber);
                    hasChanges = true;
                }
                currentNumber++;
                totalProcessed++;
            }
        }

        Logger.d("ARE_ListNumber", "Renumbered " + totalProcessed + " list items in " + listSegments.size() +
                " segments, hasChanges=" + hasChanges);
        return hasChanges;
    }

    /**
     * 将列表项按连续段分组
     * 被非列表内容分隔的列表项会被分到不同的段中
     *
     * @param editable 可编辑文本
     * @param allSpans 所有的ListNumberSpan，已按位置排序
     * @return 分组后的列表段
     */
    private java.util.List<java.util.List<ListNumberSpan>> groupListSpansIntoSegments(Editable editable, ListNumberSpan[] allSpans) {
        java.util.List<java.util.List<ListNumberSpan>> segments = new java.util.ArrayList<>();

        if (allSpans.length == 0) {
            return segments;
        }

        java.util.List<ListNumberSpan> currentSegment = new java.util.ArrayList<>();
        currentSegment.add(allSpans[0]);

        for (int i = 1; i < allSpans.length; i++) {
            ListNumberSpan prevSpan = allSpans[i - 1];
            ListNumberSpan currentSpan = allSpans[i];

            // 检查两个span之间是否连续
            if (areSpansConsecutive(editable, prevSpan, currentSpan)) {
                // 连续，添加到当前段
                currentSegment.add(currentSpan);
            } else {
                // 不连续，开始新的段
                segments.add(currentSegment);
                currentSegment = new java.util.ArrayList<>();
                currentSegment.add(currentSpan);
            }
        }

        // 添加最后一段
        if (!currentSegment.isEmpty()) {
            segments.add(currentSegment);
        }

        return segments;
    }

    /**
     * 检查两个ListNumberSpan是否连续（中间没有非列表内容）
     *
     * @param editable 可编辑文本
     * @param span1 第一个span
     * @param span2 第二个span
     * @return true如果连续，false如果被分隔
     */
    private boolean areSpansConsecutive(Editable editable, ListNumberSpan span1, ListNumberSpan span2) {
        int span1End = editable.getSpanEnd(span1);
        int span2Start = editable.getSpanStart(span2);

        return areSpansConsecutiveInternal(editable, span1End, span2Start);
    }

    /**
     * 检查ListNumberSpan和ListBulletSpan是否连续
     *
     * @param editable 可编辑文本
     * @param numberSpan ListNumberSpan
     * @param bulletSpan ListBulletSpan
     * @return true如果连续，false如果被分隔
     */
    private boolean areSpansConsecutive(Editable editable, ListNumberSpan numberSpan, ListBulletSpan bulletSpan) {
        int span1End = editable.getSpanEnd(numberSpan);
        int span2Start = editable.getSpanStart(bulletSpan);

        return areSpansConsecutiveInternal(editable, span1End, span2Start);
    }

    /**
     * 检查ListNumberSpan和UpcomingListSpan是否连续
     *
     * @param editable 可编辑文本
     * @param numberSpan ListNumberSpan
     * @param upcomingSpan UpcomingListSpan
     * @return true如果连续，false如果被分隔
     */
    private boolean areUpcomingSpanConsecutiveWithNumberSpan(Editable editable, ListNumberSpan numberSpan, UpcomingListSpan upcomingSpan) {
        int span1End = editable.getSpanEnd(numberSpan);
        int span2Start = editable.getSpanStart(upcomingSpan);

        return areSpansConsecutiveInternal(editable, span1End, span2Start);
    }

    /**
     * 检查两个位置之间是否连续（中间没有非列表内容）
     * 空白行会中断列表的连续性
     *
     * @param editable 可编辑文本
     * @param end1 第一个span的结束位置
     * @param start2 第二个span的开始位置
     * @return true如果连续，false如果被分隔
     */
    private boolean areSpansConsecutiveInternal(Editable editable, int end1, int start2) {
        if (end1 >= start2) {
            return true; // 没有间隔内容
        }

        String betweenContent = editable.subSequence(end1, start2).toString();

        // 检查中间的内容
        int newlineCount = 0;
        boolean hasNonWhitespaceContent = false;

        for (int i = 0; i < betweenContent.length(); i++) {
            char c = betweenContent.charAt(i);

            if (c == '\n') {
                newlineCount++;
            } else if (c == Constants.ZERO_WIDTH_SPACE_INT || c == ' ' || c == '\t') {
                // 跳过零宽字符、空格和制表符
                continue;
            } else {
                // 有其他非空白字符
                hasNonWhitespaceContent = true;
                break;
            }
        }

        // 检查是否有其他类型的列表span
        UpcomingListSpan[] upcomingSpans = editable.getSpans(end1, start2, UpcomingListSpan.class);
        ListBulletSpan[] bulletSpans = editable.getSpans(end1, start2, ListBulletSpan.class);
        ListNumberSpan[] numberSpans = editable.getSpans(end1, start2, ListNumberSpan.class);

        if (upcomingSpans.length > 0 || bulletSpans.length > 0 || numberSpans.length > 0) {
            Logger.d("ARE_ListNumber", "Found other list items between spans, not consecutive");
            return false;
        }

        // 如果有非空白字符内容，说明被分隔了
        if (hasNonWhitespaceContent) {
            Logger.d("ARE_ListNumber", "Found non-whitespace content between spans, not consecutive");
            return false;
        }

        // 如果有多个换行符（空白行），说明被分隔了
        if (newlineCount > 1) {
            Logger.d("ARE_ListNumber", "Found " + newlineCount + " newlines (blank lines) between spans, not consecutive");
            return false;
        }

        // 只有单个换行符或只有空白字符，认为是连续的
        Logger.d("ARE_ListNumber", "Spans are consecutive (newlines: " + newlineCount + ")");
        return true;
    }

    /**
     * 公共方法：重新编号所有列表项
     * 供外部调用
     *
     * @param editable 可编辑文本
     */
    public void reNumberAllListItemsPublic(Editable editable) {
        reNumberAllListItems(editable);
    }

    /**
     * 公共方法：重新编号所有列表项
     * 供外部调用
     *
     * @param editable 可编辑文本
     */
    public void reNumberAllListItemsPublicNoRefresh(Editable editable) {
        reNumberAllListItemsNoRefresh(editable);
    }

    /**
     * 重新编号从指定位置开始的连续列表项
     * 修复了删除中间列表项后编号不连续的问题
     * 只重新编号连续的列表项，遇到非列表内容时停止
     *
     * @param start 开始位置
     * @param editable 可编辑文本
     * @param previousNumber 前一个列表项的编号
     */
    public static void reNumberBehindListItemSpans(int start, Editable editable,
                                                   int previousNumber) {
        // 获取从start位置开始的连续列表项
        java.util.List<ListNumberSpan> consecutiveSpans = getConsecutiveListNumberSpans(start, editable);

        if (!consecutiveSpans.isEmpty()) {
            // 依次重新编号连续的列表项
            boolean hasChanges = false;
            int currentNumber = previousNumber; // 从传入的数字开始

            for (ListNumberSpan listItemSpan : consecutiveSpans) {
                int oldNumber = listItemSpan.getNumber();
                currentNumber++; // 每个span递增1
                int newNumber = currentNumber;

                if (oldNumber != newNumber) {
                    Util.log("Change old number == " + oldNumber + " to new number == " + newNumber);
                    listItemSpan.setNumber(newNumber);
                    hasChanges = true;
                }
            }

            // 如果有变化，需要触发UI刷新
            if (hasChanges) {
                Logger.d("ARE_ListNumber", "reNumberBehindListItemSpans: Renumbered " + consecutiveSpans.size() +
                        " consecutive spans, UI refresh may be needed");
            }
        }
    }

    /**
     * 获取从指定位置开始的连续列表项
     * 只返回连续的ListNumberSpan，遇到非列表内容时停止
     *
     * @param start 开始位置
     * @param editable 可编辑文本
     * @return 连续的ListNumberSpan列表
     */
    private static java.util.List<ListNumberSpan> getConsecutiveListNumberSpans(int start, Editable editable) {
        java.util.List<ListNumberSpan> consecutiveSpans = new java.util.ArrayList<>();

        if (start >= editable.length()) {
            return consecutiveSpans;
        }

        // 从start位置开始，逐行检查是否有ListNumberSpan
        String text = editable.toString();
        int currentPos = start;

        while (currentPos < editable.length()) {
            // 找到当前行的范围
            int lineStart = currentPos;
            int lineEnd = text.indexOf('\n', currentPos);
            if (lineEnd == -1) {
                lineEnd = editable.length();
            } else {
                lineEnd++; // 包含换行符
            }

            // 检查当前行是否有ListNumberSpan
            ListNumberSpan[] lineSpans = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);

            if (lineSpans != null && lineSpans.length > 0) {
                // 找到最合适的span（通常是第一个）
                ListNumberSpan lineSpan = lineSpans[0];
                consecutiveSpans.add(lineSpan);
                Logger.d("ARE_ListNumber", "Found consecutive ListNumberSpan at line [" + lineStart + "," + lineEnd + "], number=" + lineSpan.getNumber());
            } else {
                // 检查当前行是否为空行（只有换行符或空白）
                boolean isEmptyLine = true;
                for (int i = lineStart; i < lineEnd && i < editable.length(); i++) {
                    char c = editable.charAt(i);
                    if (c != '\n' && c != ' ' && c != '\t' && c != Constants.ZERO_WIDTH_SPACE_INT) {
                        isEmptyLine = false;
                        break;
                    }
                }

                // 修复：空行也应该中断列表的连续性
                if (isEmptyLine) {
                    Logger.d("ARE_ListNumber", "Found empty line at [" + lineStart + "," + lineEnd + "], stopping consecutive search (empty lines break continuity)");
                    break;
                } else {
                    // 非空行且没有ListNumberSpan，说明列表被中断了
                    Logger.d("ARE_ListNumber", "Found non-list content at [" + lineStart + "," + lineEnd + "], stopping consecutive search");
                    break;
                }
            }

            // 移动到下一行
            currentPos = lineEnd;
        }

        Logger.d("ARE_ListNumber", "Found " + consecutiveSpans.size() + " consecutive ListNumberSpans starting from position " + start);
        return consecutiveSpans;
    }


    /**
     * Change the selected {@link ListBulletSpan} to {@link ListNumberSpan}
     *
     * @param editable
     * @param listBulletSpans
     */
    protected void changeListBulletSpanToListNumberSpan(Editable editable,
                                                        ListBulletSpan[] listBulletSpans) {

        if (null == listBulletSpans || listBulletSpans.length == 0) {
            return;
        }


        // -
        // Handle this case:
        // User has:
        //
        // * AA
        // * BB
        // 1. CC
        // 2. DD
        //
        // Then user clicks Bullet icon at line 2:
        //
        // So it should change to:
        // * AA
        // 1. BB
        // 2. CC
        // 3. DD
        //
        // So this is for handling the line after 2nd line.
        // "CC" should be changed from 1 to 2
        //
        // - Restart the count after the bullet span
        int len = listBulletSpans.length;
        ListBulletSpan lastListBulletSpan = listBulletSpans[len - 1];

        // -- Remember the last list number span end
        // -- Because this list number span will be replaced with
        // -- ListBulletSpan after the loop, we won't be able to
        // -- get the last ListNumberSpan end after the replacement.
        // --
        // -- After this pos (lastListNumberSpanEnd), if there are
        // -- any ListNumberSpan, we would like to concat them with
        // -- our current editing : i.e.: we are changing the
        // -- ListBulletSpan to ListNumberSpan
        // -- If after the changing, the last ListNumberSpan's number
        // -- is X, then the following ListNumberSpan should starts
        // -- from X + 1.
        int lastListNumberSpanEnd = editable.getSpanEnd(lastListBulletSpan);

        //
        // - Replace all ListBulletSpan to ListNumberSpan
        //
        int previousListNumber = 0;

        //
        // Gets the previous list span number
        //
        // For handling this case:
        //
        // 1. AA
        // * BB
        //
        // When user clicks Number icon at line 2
        // It should change to:
        // 1. AA
        // 2. BB
        //
        // So the number of the new generated ListNumberSpan should
        // start from the previous ListNumberSpan
        ListBulletSpan firstListBulletSpan = listBulletSpans[0];
        int firstListBulletSpanStart = editable.getSpanStart(firstListBulletSpan);

        // 修复：查找前面所有的ListNumberSpan，而不是只查找很小的范围
        if (firstListBulletSpanStart > 0) {
            ListNumberSpan[] allPreviousSpans = editable.getSpans(0, firstListBulletSpanStart, ListNumberSpan.class);
            if (allPreviousSpans != null && allPreviousSpans.length > 0) {
                // 找到最接近当前位置的ListNumberSpan
                ListNumberSpan closestPreviousSpan = null;
                int maxEnd = -1;

                for (ListNumberSpan span : allPreviousSpans) {
                    int spanEnd = editable.getSpanEnd(span);
                    if (spanEnd > maxEnd && spanEnd <= firstListBulletSpanStart) {
                        maxEnd = spanEnd;
                        closestPreviousSpan = span;
                    }
                }

                if (closestPreviousSpan != null) {
                    // 检查是否与当前转换的span连续
                    if (areSpansConsecutive(editable, closestPreviousSpan, firstListBulletSpan)) {
                        previousListNumber = closestPreviousSpan.getNumber();
                        Logger.d("ARE_ListNumber", "changeListBulletSpanToListNumberSpan: Found consecutive previous span with number " + previousListNumber);
                    } else {
                        Logger.d("ARE_ListNumber", "changeListBulletSpanToListNumberSpan: Previous span not consecutive, starting from 0");
                    }
                }
            }
        }

        for (ListBulletSpan listBulletSpan : listBulletSpans) {
            int start = editable.getSpanStart(listBulletSpan);
            int end = editable.getSpanEnd(listBulletSpan);

            editable.removeSpan(listBulletSpan);
            previousListNumber++;
            ListNumberSpan listNumberSpan = new ListNumberSpan(previousListNumber);
            editable.setSpan(listNumberSpan, start, end,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        }

        // 修复：转换完成后，重新编号整个文档以确保正确的分段编号
        Logger.d("ARE_ListNumber", "changeListBulletSpanToListNumberSpan: Triggering global renumbering after conversion");
        reNumberAllListItems(editable);
    }


    /**
     * Change the selected {@link ListBulletSpan} to {@link ListNumberSpan}
     *
     * @param editable
     * @param upcomingListSpans
     */
    protected void changeUpcomingToListNumberSpan(Editable editable,
                                                  UpcomingListSpan[] upcomingListSpans) {

        if (null == upcomingListSpans || upcomingListSpans.length == 0) {
            return;
        }

        // - Restart the count after the bullet span
        int len = upcomingListSpans.length;
        UpcomingListSpan lastUpcomingSpan = upcomingListSpans[len - 1];

        // -- Remember the last list number span end
        // -- Because this list number span will be replaced with
        // -- ListBulletSpan after the loop, we won't be able to
        // -- get the last ListNumberSpan end after the replacement.
        // --
        // -- After this pos (lastListNumberSpanEnd), if there are
        // -- any ListNumberSpan, we would like to concat them with
        // -- our current editing : i.e.: we are changing the
        // -- ListBulletSpan to ListNumberSpan
        // -- If after the changing, the last ListNumberSpan's number
        // -- is X, then the following ListNumberSpan should starts
        // -- from X + 1.
        int lastListNumberSpanEnd = editable.getSpanEnd(lastUpcomingSpan);

        //
        // - Replace all ListBulletSpan to ListNumberSpan
        //
        int previousListNumber = 0;

        //
        // Gets the previous list span number
        //
        // For handling this case:
        //
        // 1. AA
        // * BB
        //
        // When user clicks Number icon at line 2
        // It should change to:
        // 1. AA
        // 2. BB
        //
        // So the number of the new generated ListNumberSpan should
        // start from the previous ListNumberSpan
        UpcomingListSpan firstUpcomingSpan = upcomingListSpans[0];
        int firstListBulletSpanStart = editable.getSpanStart(firstUpcomingSpan);

        // 修复：查找前面所有的ListNumberSpan，而不是只查找很小的范围
        if (firstListBulletSpanStart > 0) {
            ListNumberSpan[] allPreviousSpans = editable.getSpans(0, firstListBulletSpanStart, ListNumberSpan.class);
            if (allPreviousSpans != null && allPreviousSpans.length > 0) {
                // 找到最接近当前位置的ListNumberSpan
                ListNumberSpan closestPreviousSpan = null;
                int maxEnd = -1;

                for (ListNumberSpan span : allPreviousSpans) {
                    int spanEnd = editable.getSpanEnd(span);
                    if (spanEnd > maxEnd && spanEnd <= firstListBulletSpanStart) {
                        maxEnd = spanEnd;
                        closestPreviousSpan = span;
                    }
                }

                if (closestPreviousSpan != null) {
                    // 检查是否与当前转换的span连续
                    if (areUpcomingSpanConsecutiveWithNumberSpan(editable, closestPreviousSpan, firstUpcomingSpan)) {
                        previousListNumber = closestPreviousSpan.getNumber();
                        Logger.d("ARE_ListNumber", "changeUpcomingToListNumberSpan: Found consecutive previous span with number " + previousListNumber);
                    } else {
                        Logger.d("ARE_ListNumber", "changeUpcomingToListNumberSpan: Previous span not consecutive, starting from 0");
                    }
                }
            }
        }

        for (UpcomingListSpan upcomingListSpan : upcomingListSpans) {
            int start = editable.getSpanStart(upcomingListSpan);
            int end = editable.getSpanEnd(upcomingListSpan);

            StrikethroughSpan[] strikethroughSpans = mEditText.getText().getSpans(start,end,StrikethroughSpan.class);
            ForegroundColorSpan[] foregroundColorSpans = mEditText.getText().getSpans(start,end,ForegroundColorSpan.class);

            if (strikethroughSpans.length > 0) {
                mEditText.getText().removeSpan(strikethroughSpans[0]);
            }
            if (foregroundColorSpans.length > 0) {
                mEditText.getText().removeSpan(foregroundColorSpans[0]);
            }

            editable.removeSpan(upcomingListSpan);
            previousListNumber++;
            ListNumberSpan listNumberSpan = new ListNumberSpan(previousListNumber);
            editable.setSpan(listNumberSpan, start, end,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        }

        // 修复：转换完成后，重新编号整个文档以确保正确的分段编号
        Logger.d("ARE_ListNumber", "changeUpcomingToListNumberSpan: Triggering global renumbering after conversion");
        reNumberAllListItems(editable);
    }

}