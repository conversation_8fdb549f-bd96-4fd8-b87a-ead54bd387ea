package com.tcl.ai.note.handwritingtext.vm.text

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.util.Size
import android.view.View
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.event.UiSateEvent
import com.tcl.ai.note.handwritingtext.controller.NoteDataSaveController
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
import com.tcl.ai.note.handwritingtext.repo.EntHelper
import com.tcl.ai.note.handwritingtext.repo.EntRepository
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.repo.idToEntPath
import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.history.RichTextEditOperation
import com.tcl.ai.note.handwritingtext.richtext.history.UndoStackOp
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogCallBackEvent
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEventManager
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.state.RichTextDataState
import com.tcl.ai.note.handwritingtext.vm.text.converter.RichTextBlockConverter
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.delegate
import com.tcl.ai.note.utils.delegateViewFunc
import com.tcl.ai.note.utils.isFileExists
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.runDefault
import com.tcl.ai.note.utils.runIO
import com.tcl.ai.note.utils.screenSizeMax
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * RichTextViewModel2
 * 二期富文本数据 ViewModel，适配 Room+Compose，支持老数据迁移和新富文本写入。
 * 支持文本块样式/待办块迁移为新的富文本结构，并保留图片和音频块。
 */
class RichTextViewModel2 : ViewModel() {
    // 当前文本选择范围
    data class SelectionRange(val start: Int, val end: Int)
    private val _selectionRangeState = MutableStateFlow(SelectionRange(0, 0))
    var selectionRange: SelectionRange
        get() = _selectionRangeState.value
        set(value) { _selectionRangeState.value = value }

    // 请求焦点的事件流
    private val _requestFocusEvent = MutableSharedFlow<Unit>(extraBufferCapacity = 1)
    val requestFocusEvent = _requestFocusEvent.asSharedFlow()

    // 获取富文本的实际宽高
    val mRichTextSizeState = MutableStateFlow<Size>(Size(0, 0))
    val richTextSizeState = mRichTextSizeState.asStateFlow()
    var richTextSize by mRichTextSizeState.delegate()

    // 当前笔记ID
    var mNoteId by mutableStateOf<Long?>(null)
        private set

    // 富文本UI总状态流
    private val _uiState = MutableStateFlow(RichTextDataState())
    val uiState: StateFlow<RichTextDataState> = _uiState.asStateFlow()

    // 富文本复制
    private val richTextCopyLoadingState = MutableStateFlow(false)
    val richTextCopyLoading = richTextCopyLoadingState.asStateFlow()

    // 复制文本任务
    var copyJob: Job? = null
    private var showLoadingJob: Job? = null

    /**
     * 请求富文本输入框获取焦点
     */
    private fun requestFocus() {
        _requestFocusEvent.tryEmit(Unit)
    }

    private fun observeCategoryDialogEvents() {
        viewModelScope.launch {
            CategoryDialogEventManager.categoryDialogSuccessEvents.collect { event ->
                if (event is CategoryDialogCallBackEvent.OnNoteMovedSuccessful) {
                    Logger.i(TAG, "Note Move to New Category successfully callback: categoryId=${event.categoryId}")
                    _uiState.update {
                        it.copy(categoryId = event.categoryId.toLongOrNull()?:1L)
                    }
                }
            }
        }
    }

    // 富文本追加批量换行事件流
    private val _insertLineBreakEvent = MutableSharedFlow<Int>(extraBufferCapacity = 8)
    val insertLineBreakEvent = _insertLineBreakEvent.asSharedFlow()

    /**
     * 监听是否有文本变化、格式转换操作
     *
     * 注：不包含undo和redo导致的文本变化
     */
    private val mUndoStackChangedShared = MutableSharedFlow<Pair<RichTextEditOperation, UndoStackOp>>()
    val undoStackChangedShared = mUndoStackChangedShared.asSharedFlow()
    fun RichTextController.onUndoStackAdded(richTextEditOperation: RichTextEditOperation) = viewModelScope.launch {
        mUndoStackChangedShared.emit(richTextEditOperation to UndoStackOp.Add)
    }
    fun RichTextController.onUndoStackRemoved(richTextEditOperation: RichTextEditOperation) = viewModelScope.launch {
        mUndoStackChangedShared.emit(richTextEditOperation to UndoStackOp.Remove)
    }

    /**
     * 撤销
     */
    private val undoFuncDelegate = delegateViewFunc<Unit>()
    var View.implUndoFunc by undoFuncDelegate
    fun undo() {
        undoFuncDelegate.invokeFunc()
    }

    /**
     * 操作
     */
    private val redoFuncDelegate = delegateViewFunc<Unit>()
    var View.implRedoFunc by redoFuncDelegate
    fun redo() {
        redoFuncDelegate.invokeFunc()
    }

    /**
     * 操作
     */
    private val resetPasteFuncDelegate = delegateViewFunc<Unit>()
    var View.implResetPasteFunc by resetPasteFuncDelegate
    fun resetPaste() {
        resetPasteFuncDelegate.invokeFunc()
    }

    /**
     * 触发追加换行请求
     */
    fun addLineBreaksAtEnd(n: Int) {
        if (n <= 0) return
        _insertLineBreakEvent.tryEmit(n)
    }

    /**
     * 设计getLineHeightDelegate接口并实现
     *
     * 设置view的可见性
     */
    private val getLineHeightDelegate = delegateViewFunc<Int>()
    // 只有view可见，设置getLineHeightDelegate属性
    var View.implGetLineHeight by getLineHeightDelegate
    fun getLineHeight(): Int {
        return getLineHeightDelegate.invokeFunc()
    }

    /**
     * 修改缩放信息
     *
     * 设置view的可见性
     */
    private val changeScaleDelegate = delegateViewFunc<MatrixInfo, Unit>()
    var View.implChangeScale by changeScaleDelegate
    fun changeScaleInfo(matrixInfo: MatrixInfo) {
        Logger.d(TAG, "changeScaleInfo: $matrixInfo")
        changeScaleDelegate.invokeFunc(matrixInfo)
    }

    private val toHtmlDelegate = delegateViewFunc<String?>()
    var View.implToHtml by toHtmlDelegate
    suspend fun toHtml() = runDefault { toHtmlDelegate.invokeFunc() }

    /**
     * 新建或加载已有笔记：
     * - id为空自动新建
     * - id存在则加载数据库内容
     * - 支持老数据（TextBlock/TodoBlock）自动迁移为RichTextV2
     * - 支持老的手绘数据自动迁移展示
     * - 富文本V2已存在直接取
     * - 图片、音频块原样展示
     */
    fun createOrGetNote(noteId: Long?) {
        viewModelScope.launchIO {
            try {
                if (noteId == null || NoteRepository2.get(noteId) == null) {
                    delay(150)//转场动画未结束，就创建了笔记，出现首页显示A,稍微延时创建一下
                    // id为空，新建空白笔记
                    val categoryId = AppDataStore.getData("currentCategoryId", "").toLongOrNull()?:1L
                    Logger.i(TAG,"createOrGetNote, categoryId:$categoryId")
                    <EMAIL> = NoteRepository2.insert(
                        Note(
                            title = "",
                            categoryId = categoryId
                        )
                    )
                    _uiState.value = RichTextDataState()  // 空白状态

                    _uiState.update { it.copy(noteId = mNoteId, isNewNote = true, categoryId = categoryId) }
                    // 通知UI加载
                    RichTextEventManager.triggerTextOperateEvent(
                        RichTextOperateEvent.LoadNote(_uiState.value.content,_uiState.value.richTextStyleEntity, true)
                    )
                } else {
                    val note = NoteRepository2.get(noteId) ?: return@launchIO
                    val contentBlocks = note.contents

                    try {
                        // 提取二期富文本结构align {java.util.List} ... get()
                        val richV2 = contentBlocks.filterIsInstance<EditorContent.RichTextV2>().firstOrNull()
                        // 分割图片和音频数据
                        val images = contentBlocks.filterIsInstance<EditorContent.ImageBlock>()
                        val audios = contentBlocks.filterIsInstance<EditorContent.AudioBlock>()
                        // 还原图片辅助
                        var entHelpers = emptyList<EntHelper>()
                        val text = note.content
                        val title = note.title
                        if (richV2 != null) {
                            // 二期格式直接还原
                            _uiState.value = RichTextDataState(
                                noteId = noteId,
                                title = title,
                                content = text,
                                richTextStyleEntity = richV2.richTextStyleEntity,
                                images = images,
                                audios = audios,
                                categoryId = note.categoryId,
                                bgMode = note.bgMode,
                                bgColor = note.bgColor,
                                isNewNote = false,
                                isShow = note.isShow
                            )
                        }else{
                            // 一期老数据块，迁移为V2富文本结构
                            // 一期老数据，需要迁移TextBlock/TodoBlock所有块
                            val textBlocks = contentBlocks.filter {
                                it is EditorContent.TextBlock
                                        || it is EditorContent.TodoBlock
                                        || it is EditorContent.ImageBlock
                            }
                            // 将一期老数据（TextBlock/TodoBlock）转换成v2数据结构
                            val richResult = RichTextBlockConverter.convertBlocksToRichTextV2(textBlocks)
                            entHelpers = richResult.entHelpers
                            _uiState.value = RichTextDataState(
                                noteId = noteId,
                                title = note.title,
                                content = richResult.plainText,
                                richTextStyleEntity = richResult.richEntity,
                                images = images,
                                audios = audios,
                                categoryId = note.categoryId,
                                bgMode = note.bgMode,
                                bgColor = note.bgColor,
                                isNewNote = false,
                                isShow = note.isShow
                            )
                        }

                        // 通知UI加载富文本数据
                        RichTextEventManager.triggerTextOperateEvent(
                            RichTextOperateEvent.LoadNote(_uiState.value.content,_uiState.value.richTextStyleEntity, true)
                        )

                        // 富文本加载完后加载手绘，临时使用线性避免并发问题
                        // 手绘数据自动迁移
                        // id不为空，有旧数据，尝试读取
                        handWritingV1ToSingleEntV2(noteId, entHelpers)
                    } catch (e: Exception) {
                        Logger.e(TAG, "load Note Exception:${e.message}")
                        // 恢复最小内容
                        _uiState.value = RichTextDataState(noteId = noteId, title = note.title)
                    }
                    <EMAIL> = noteId
                }
            }catch (e: Exception){
                Logger.e(TAG, "createOrGetNote exception:${e.message}")
                _uiState.value = RichTextDataState()
            }
        }
    }

    /**
     * 一期手绘转二期手绘
     */
    private suspend fun handWritingV1ToSingleEntV2(noteId: Long, entHelpers: List<EntHelper>) = runCatching {
        // 富文本加载完后加载手绘，临时使用线性避免并发问题
        // 手绘数据自动迁移
        // id不为空，有旧数据，尝试读取
        val singleEntPath = noteId.idToEntPath()
        val singleEntFile = File(singleEntPath)
        singleEntFile.parentFile?.mkdirs()
        // 这个文件用来标记是否转换完成
        val singleEntConvertFlagPath = "$singleEntPath.convert"
        val singleEntConvertFlagFile = File(singleEntConvertFlagPath)
        // 转换中途,进程被删了，重新开始转换。
        if (singleEntConvertFlagFile.exists()) {
            singleEntFile.delete()
            singleEntConvertFlagFile.delete()
        }
        if (!singleEntFile.exists()) {
            val start = System.currentTimeMillis()
            Logger.v(TAG, "handWritingV1ToSingleEntV2, start. draw not exist, try to migrate: $singleEntPath")
            DrawBoardRepository.getDrawByNoteIdBlock(noteId)?.let { draw ->
                // 笔画，先缩放一遍，对齐sunia
                val screenWidth =
                    if (isTablet) screenSizeMax
                    else screenSizeMin
                val scale = screenWidth / 1000f
                val toEntStrokes = draw.strokes.map { stroke ->
                    val scaleStroke = stroke.points.map {
                        it.copy(x = it.x / scale, y = it.y / scale)
                    }
                    val scaleWidthStyle =
                        stroke.style.copy(width = stroke.style.width / scale)
                    stroke.copy(
                        points = scaleStroke,
                        style = scaleWidthStyle
                    )
                }
                Logger.v(TAG, "handWritingV1ToSingleEntV2, noteId: $noteId, recover image data to ent, origin image size and text layout: $entHelpers")
                singleEntConvertFlagFile.createNewFile()
                EntRepository.writeEntData(draw.copy(strokes = toEntStrokes), singleEntPath, entHelpers)
                singleEntConvertFlagFile.delete()
                Logger.v(TAG, "handWritingV1ToSingleEntV2 cost: ${System.currentTimeMillis() - start} ms")
            }
        }
    }.onFailure {
        Logger.e(TAG, "handWritingV1ToSingleEntV2 error: ${it.stackTraceToString()}")
    }

    /**
     * 保存当前UI内容到Note表。
     * - 合成富文本(images/audios/rich)并存入contents
     * - 最新plain文本拼接
     */
    internal fun saveNote() = NoteDataSaveController.saveRichText {
        try {
            val state = _uiState.value
            val contents = mutableListOf<EditorContent>().apply {
                addAll(state.audios)
                addAll(state.images)
                add(EditorContent.RichTextV2(state.richTextStyleEntity))
            }

            val plainText = state.content
            val summary = plainText.take(300)
            // 第一张图片 改成新添加的最后一张图片
            val firstPicture = state.images.lastOrNull()?.uri?.toString()
            // 有无音频
            val hasAudio = state.audios.isNotEmpty()
            // 分类
            val categoryId = state.categoryId ?: 1L
            // 皮肤
            val bgMode = state.bgMode ?: BgMode.none
            val bgColor = state.bgColor ?: Skin.defColor

            val now = System.currentTimeMillis()
            val isEmptyContent = NoteContentUtil.isContentEmpty(state)

            try {
                if (state.noteId == null) {
                    // 新增 Note
                    val newNote = Note(
                        title = state.title,
                        content = plainText,
                        contents = contents,
                        summary = summary,
//                        firstPicture = firstPicture,//二期不用，来区分是一期的值
                        handwritingThumbnail = null,
                        hasAudio = hasAudio,
                        categoryId = categoryId,
                        createTime = now,
                        modifyTime = now,
                        bgMode = bgMode,
                        bgColor = bgColor,
                        deleteFlag = false,
                        isShow = !isEmptyContent || state.isShow
                    )
                    val id = NoteRepository2.insert(newNote)
                    _uiState.update { it.copy(noteId = id, isSaved = true) }
                } else {
                    // 覆盖更新
                    val oldNote = NoteRepository2.get(state.noteId) ?: return@saveRichText
                    val newNote = oldNote.copy(
                        title = state.title,
                        content = plainText,
                        contents = contents,
                        summary = summary,
//                        firstPicture = firstPicture,//二期不用，来区分是一期的值
//                        handwritingThumbnail = HandWritingThumbnailRepo.getBitmapPath(state.noteId),// 不在这里赋值，在缩略图生成时赋值
                        hasAudio = hasAudio,
                        categoryId = categoryId,
                        modifyTime = now,
                        bgMode = bgMode,
                        bgColor = bgColor,
                        deleteFlag = false,
                        isShow = !isEmptyContent || state.isShow
                    )
                    NoteRepository2.update(newNote)
                    _uiState.update { it.copy(isSaved = true) }
                }
            } catch (e: Exception) {
                Logger.e(TAG, "data operate exception:${e.message}")
            }
        } catch (ex: Exception) {
            Logger.e(TAG, "saveNote exception:${ex.message}")
        }

    }

    /**
     * 删除一条Note
     */
    suspend fun deleteOneNote(noteId:Long):Int{
        return NoteRepository2.deleteOneNote(noteId)
//        return NoteRepository2.logicDelete(noteId)
    }

    /**
     * 隐藏一条Note
     */
    suspend fun hideNote(noteId:Long):Int{
        return NoteRepository2.hideNote(noteId)
    }

    /**
     * 富文本变化（内容或样式）,实时保存到state流
     */
    fun onRichTextChanged(content: String, style: RichTextStyleEntity) {
        _uiState.update { old ->
            old.copy(content = content, richTextStyleEntity = style, isSaved = true)
        }
        saveNote()
    }

    /**
     * 标题变化，自动保存
     */
    fun onTitleChanged(newTitle: String) {
        _uiState.update { it.copy(title = newTitle, isSaved = true) }
        // 强制立即保存
        saveNote()
    }

    /**
     * 更新Skin状态
     */
     fun onSkinStyleChanaged(bgMode: BgMode,bgColor: Long){
        //Logger.d(TAG, "onSkinStyleChanaged: bgMode=$bgMode, bgColor=$bgColor")
        _uiState.update {
            it.copy(bgMode = bgMode, bgColor = bgColor)
        }
        saveNote()
    }

    /**
     * 图片变化，自动保存
     */
    fun onImagesChanged(newImages: List<EditorContent.ImageBlock>) {
        _uiState.update { it.copy(images = newImages) }
        saveNote()
    }

    /**
     * 录音变化，自动保存
     */
    fun onAudiosChanged(newAudios: List<EditorContent.AudioBlock>) {
        _uiState.update { it.copy(audios = newAudios, isSaved = true) }
        saveNote()
    }

    fun onRenameAudio(oldAudioPath: String, newAudioPath: String) {
        _uiState.update { state ->
            val newAudios = state.audios.map { audio ->
                if (audio.audioPath == oldAudioPath) {
                    audio.copy(audioPath = newAudioPath, audioDuration = audio.audioDuration)
                } else {
                    audio
                }
            }
            state.copy(audios = newAudios)
        }
        saveNote()
    }

    fun onDeleteAudio(audioPath: String) {
        viewModelScope.launch {
            _uiState.update { state ->
                val newAudios = state.audios.filterNot { it.audioPath == audioPath}
                    .filterNot { if(it.audioDuration == 0L) getAudioDuration(it.audioPath) < 2000L else it.audioDuration <2000L }
                state.copy(audios = newAudios)
            }
        }
        saveNote()
    }


    fun startCopy(start: Int, end: Int, text: CharSequence) {
        copyJob?.cancel()
        copyJob = viewModelScope.launch {
            try {
                showLoadingJob()
                val time = System.currentTimeMillis()
                Logger.d(TAG, "copy start")
                val clipboard =
                    GlobalContext.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

                val styleEntity = _uiState.value.richTextStyleEntity
                val spannableString = RichTextStyleEntityToSpanConverter.applyToCopyRichText(
                    styleEntity,
                    start,
                    end,
                    text
                )
                var clip = ClipData.newPlainText("label", spannableString) // 对于富文本，需要使用下面的方法
                clipboard.setPrimaryClip(clip)
                Logger.d(TAG, "copy success  time:${System.currentTimeMillis() - time}")
            } catch (e: Exception) {
                Logger.e(TAG, "copy exception:${e.message}")
            } finally {
                dismissLoadingJobWithDelay()
            }
        }
    }


    fun startPates(onPaste: () -> Unit) {
        copyJob?.cancel()
        copyJob = viewModelScope.launch {
            try {
                showLoadingJob()
                Logger.d(TAG, "start Paste")
                coroutineScope {
                    runIO {
                        onPaste.invoke()
                    }
                }
                Logger.d(TAG, "Paste end")
            } catch (e: Exception) {
                Logger.e(TAG, "copy exception:${e.message}")
            } finally {
                dismissLoadingJobWithDelay()
            }
        }
    }

    fun cancelCopyOrPaste() {
        richTextCopyLoadingState.update { false }
        copyJob?.cancel()
        Logger.d(TAG, "reset paste")
        resetPaste()
    }

    private fun showLoadingJob() {
        showLoadingJob?.cancel()
        showLoadingJob = viewModelScope.launch {
            Logger.d(TAG, "start showLoadingJob")
            delay(500)
            richTextCopyLoadingState.update { true }
            Logger.d(TAG, "showLoadingJob: ${richTextCopyLoadingState.value}")
        }
    }

    private suspend fun dismissLoadingJobWithDelay() {
        // 如果已经显示，那延迟一段时间，避免闪现
        if (richTextCopyLoadingState.value) {
            delay(500)
        }
        richTextCopyLoadingState.update { false }
        showLoadingJob?.cancel()
    }

    init {
        observeCategoryDialogEvents()
        observeUiStateChanges()
    }

    private fun observeUiStateChanges() {
        viewModelScope.launch {
            _uiState.collect { newState ->
                // 发送状态变化事件
                UiSateEvent.sendUiStateChangeCallBackEvent(newState.audios.size)
            }
        }
    }

    companion object {
        private const val TAG = "RichTextViewModel2"
    }

}