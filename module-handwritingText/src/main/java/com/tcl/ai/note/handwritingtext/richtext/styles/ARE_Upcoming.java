package com.tcl.ai.note.handwritingtext.richtext.styles;

import static java.lang.Math.min;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.Editable;
import android.text.Layout;
import android.text.Spannable;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.Commands.AsyncResult;
import com.tcl.ai.note.handwritingtext.richtext.Commands.PM_Commands;
import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter;
import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextParagraphUtil;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

/**
 * author: xuyuan
 * created on:19-7-29 下午3:01
 * description:
 */
public class ARE_Upcoming implements IARE_Style {
    private final static String TAG = ARE_Upcoming.class.getSimpleName();
    private ImageView mUpcomingView;
    private AREditText mEditText;
    private boolean mIsUpcomingCheck = false;
    private boolean mIsUpcomingValid = false;
    private Context mContext;
//    private UpcomingItem mUpcomingItem;


    public interface OnCheckStateListener {
        void onCheckState(boolean isChecked);
    }

    private OnCheckStateListener mOnCheckStateListener;

    public static final Class<?> SPAN_CLASS = ARE_Upcoming.class;

    public ARE_Upcoming(Context context) {
        mContext = context;
    }

    public void registerUpcomingMessage() {
        PM_Commands.getInstance().setTextRegistrant(handler, PM_Commands.EVENT_TODO_SPAN_CLICK, null);
        PM_Commands.getInstance().setTextRegistrant(handler, PM_Commands.EVENT_TODO_IMAGE_CLICK, null);
    }

    public void unRegisterUpcomingMessage() {
        PM_Commands.getInstance().unSetTodoRegistrant(handler);
    }

    public void setOnCheckStateListener(OnCheckStateListener mOnCheckStateListener) {
        this.mOnCheckStateListener = mOnCheckStateListener;
    }

    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            Logger.d("ARE_Upcoming handleMessage , msg.what=" + msg.what);
            switch (msg.what) {
                case PM_Commands.EVENT_TODO_SPAN_CLICK: {
                    mEditText.stopApplyMonitor();
                    mEditText.stopStorageMonitor();
                    mEditText.startUiShowMonitor();

                    AsyncResult ar = (AsyncResult) msg.obj;
                    UpcomingListSpan upcomingListSpan = (UpcomingListSpan) ar.result;
                    toggleStrikeboundSpan(upcomingListSpan.isChecked(), upcomingListSpan);

                    int end = mEditText.getText().getSpanEnd(upcomingListSpan);
                    mEditText.shouldFixCursor(end);

                    triggerTextChange();
                    if (mOnCheckStateListener != null) {
                        mOnCheckStateListener.onCheckState(upcomingListSpan.isChecked());
                    }
                }
                break;
                case PM_Commands.EVENT_TODO_IMAGE_CLICK: {

                }
                break;
                default:
                    break;
            }
        }
    };

    public void toggleStrikeboundSpan(boolean isCheck, UpcomingListSpan upcomingListSpan) {
        int start = mEditText.getText().getSpanStart(upcomingListSpan);
        // mEditText光标所在处
        int selectionEnd = mEditText.getSelectionEnd();
        int currentLine = mEditText.getLayout().getLineForOffset(start);
        start = Util.getThisLineStart(mEditText, currentLine);

        int end = Util.getThisLineEnd(mEditText, currentLine);

        //int end = mEditText.getText().getSpanEnd(upcomingListSpan);
        if (start == -1 || end == -1) {
            return;
        }
        if (isCheck) {
            setStrikeboundSpan(start, end);
        } else {
            removeStrikeboundSpan(start, end);
        }
        //点击选中待办事项的时候，需要记录到撤销栈中 只记录最后位置，不然就有选中效果
        Logger.d("ARE_Upcoming toggleStrikeboundSpan upcoming selectionEnd =" + selectionEnd + " start/end=" + start + "/" + end + " isCheck=" + isCheck);
        RichTextKTUtilsKt.recordApplyStyleToUndoRedo(end, end, isCheck, mEditText, UpcomingListSpan.class);
    }

    private void removeStrikeboundSpan(int start, int end) {
        StrikethroughSpan[] strikethroughSpans = mEditText.getText().getSpans(start, end, StrikethroughSpan.class);
        ForegroundColorSpan[] foregroundColorSpans = mEditText.getText().getSpans(start, end, ForegroundColorSpan.class);
        Logger.d("removeStrikeboundSpan upcoming start/end=" + start + "/" + end + " strikeLength=" + strikethroughSpans.length
                + " foreColorLength=" + foregroundColorSpans.length);
        for (Object obj : strikethroughSpans) {
            mEditText.getText().removeSpan(obj);
        }
        for (Object obj : foregroundColorSpans) {
            mEditText.getText().removeSpan(obj);
        }
    }

    public void setStrikeboundSpan(int start, int end) {
        Editable editable = mEditText.getText();
        // 移除目标范围内所有旧的删除线和颜色 span
        StrikethroughSpan[] strikethroughSpans = editable.getSpans(start, end, StrikethroughSpan.class);
        for (StrikethroughSpan s : strikethroughSpans) {
            editable.removeSpan(s);
        }
        ForegroundColorSpan[] foregroundColorSpans = editable.getSpans(start, end, ForegroundColorSpan.class);
        for (ForegroundColorSpan f : foregroundColorSpans) {
            editable.removeSpan(f);
        }

        // 重新添加覆盖整个范围的删除线
        StrikethroughSpan strikeSpan = new StrikethroughSpan();
        editable.setSpan(strikeSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        // 重新添加字体颜色
        ForegroundColorSpan foregroundSpan = new ForegroundColorSpan(
                mContext.getColor(com.tcl.ai.note.base.R.color.rt_todo_block_done_color));
        editable.setSpan(foregroundSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
    }


    @Override
    public void setListenerForImageView(ImageView imageView) {
        imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }

    /**
     * 设置todo 样式
     */
    public void setTodo(){
        if (!getIsValid()) {
            return;
        }
        int selectionStart = mEditText.getSelectionStart();
        int selectionEnd = mEditText.getSelectionEnd();
        try {
            Logger.d("datahub, todo_click");
            //mEditText.setLineSpaceExtra(CommonUtils.dip2px(5));
            mEditText.stopUiSHowMonitor();
            mIsUpcomingCheck = !mIsUpcomingCheck;
            mEditText.updateParagraphCheckStatus(ARE_Upcoming.this, mIsUpcomingCheck);
            mEditText.stopApplyMonitor();
            mEditText.stopStorageMonitor();
            Logger.d(TAG, "ARE_Upcoming begin onClick, spanSize="
                    + mEditText.getEditableText().getSpans(0,
                    mEditText.getEditableText().length(), Object.class).length);
            int currentLine = Util.getCurrentCursorLine(mEditText);
            int start = Util.getThisLineStart(mEditText, currentLine);
            int end = Util.getThisLineEnd(mEditText, currentLine);

            Editable editable = mEditText.getText();

            int selectionStartLine = Util.getLineBySelection(mEditText, selectionStart);
            int selectionEndLine = Util.getLineBySelection(mEditText, selectionEnd);

            // -----case 有多行同时选中场景
            if (selectionStart != -1 && selectionEnd != -1 && selectionStartLine < selectionEndLine) {
                if(mIsUpcomingCheck){
                    RichTextParagraphUtil.setParagraphSpanByLine(mEditText, editable, selectionStart, selectionEnd, UpcomingListSpan.class);
                }else{
                    RichTextParagraphUtil.clearParagraphSpansByLine(mEditText, editable, selectionStart, selectionEnd);
                }
                triggerTextChange();
                RichTextKTUtilsKt.recordApplyStyleToUndoRedo(selectionStart, selectionEnd, getIsChecked(), mEditText, ARE_Upcoming.SPAN_CLASS);
                return;
            }

            // -----case 有多行同时选中场景

            //
            // Check if there is any ListNumberSpan first.
            // If there is ListNumberSpan, it means this case:
            // User has typed in:
            //
            // 1. aa
            // 2. bb
            // 3. cc
            //
            // Then user clicks the Bullet icon at 1 or 2 or any other item
            // He wants to change current ListNumberSpan to UpcomingSpan
            //
            // So it becomes:
            // For example: user clicks Bullet icon at 2:
            // 1. aa
            // * bb
            // 1. cc
            //
            // Note that "cc" has been restarted from 1

            // 处理单行逻辑
            ListNumberSpan[] listNumberSpans = editable.getSpans(selectionStart,
                    selectionEnd, ListNumberSpan.class);
            ListBulletSpan[] listBulletSpans = editable.getSpans(selectionStart,
                    selectionEnd, ListBulletSpan.class);
            if (null != listNumberSpans && listNumberSpans.length > 0) {
                changeListNumberSpanToUpcomingSpan(editable, listNumberSpans);
                triggerTextChange();
                RichTextKTUtilsKt.recordApplyStyleToUndoRedo(selectionStart, selectionEnd, getIsChecked(), mEditText, ARE_Upcoming.SPAN_CLASS);
                Logger.d(TAG, "ARE_Upcoming change onClick, spanSize=" + mEditText.getEditableText().getSpans(0, mEditText.getEditableText().length(), Object.class).length);
                return;
            } else if (null != listBulletSpans && listBulletSpans.length > 0) {
                changeListBulletSpanToUpComingSpan(editable, listBulletSpans);
                triggerTextChange();
                RichTextKTUtilsKt.recordApplyStyleToUndoRedo(selectionStart, selectionEnd, getIsChecked(), mEditText, ARE_Upcoming.SPAN_CLASS);
                return;
            }

            // -----case 有多行同时选中场景


            //
            // Normal cases
            //
            UpcomingListSpan[] UpcomingSpans = editable.getSpans(start,
                    end, UpcomingListSpan.class);
            if (null == UpcomingSpans || UpcomingSpans.length == 0) {
                //
                // Current line is not list item span
                // By clicking the image view, we should make it as
                // BulletListItemSpan
                // And ReOrder
                //
                // ------------- CASE 1 ---------------
                // Case 1:
                // Nothing types in, user just clicks the List image
                // For this case we need to mark it as BulletListItemSpan

                //
                // -------------- CASE 2 --------------
                // Case 2:
                // Before or after the current line, there are already
                // BulletListItemSpan have been made
                // Like:
                // 1. AAA
                // BBB
                // 1. CCC
                //
                // User puts cursor to the 2nd line: BBB
                // And clicks the List image
                // For this case we need to make current line as
                // BulletListItemSpan
                // And, we should also reOrder them as:
                //
                // 1. AAA
                // 2. BBB
                // 3. CCC
                //

                //
                // Case 2
                //
                // There are list item spans ahead current editing
                UpcomingListSpan[] aheadListItemSpans = editable.getSpans(
                        start - 2, start - 1, UpcomingListSpan.class);
                if (null != aheadListItemSpans
                        && aheadListItemSpans.length > 0) {
                    UpcomingListSpan previousListItemSpan = aheadListItemSpans[aheadListItemSpans.length - 1];
                    if (null != previousListItemSpan) {
                        int pStart = editable
                                .getSpanStart(previousListItemSpan);
                        int pEnd = editable
                                .getSpanEnd(previousListItemSpan);
                        //
                        // Handle this case:
                        // 1. A
                        // B
                        // C
                        // 1. D
                        //
                        // User puts focus to B and click List icon, to
                        // change it to:
                        // 2. B
                        //
                        // Then user puts focus to C and click List icon, to
                        // change it to:
                        // 3. C
                        // For this one, we need to finish the span "2. B"
                        // correctly
                        // Which means we need to set the span end to a
                        // correct value
                        // This is doing this.
                        if (pEnd == 0) {
                            editable.removeSpan(previousListItemSpan);
                        } else if (editable.charAt(pEnd - 1) == Constants.CHAR_NEW_LINE) {
                            editable.removeSpan(previousListItemSpan);
                            editable.setSpan(previousListItemSpan, pStart,
                                    pEnd - 1,
                                    Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                        }

                        makeLineAsUpcoming();
                    }
                } else {
                    //
                    // Case 1
                    makeLineAsUpcoming();
                }
            } else {
                //
                // Current line is list item span
                // By clicking the image view, we should remove the
                // BulletListItemSpan
                //

                int spanEnd = editable.getSpanEnd(UpcomingSpans[0]);
                int spanStart = editable.getSpanStart(UpcomingSpans[0]);

                removeStrikeboundSpan(spanStart, spanEnd);
                editable.removeSpan(UpcomingSpans[0]);

                if (editable.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT)
                    editable.delete(spanStart, spanStart + 1);

                updateCheckStatus(false);
            }
        } catch (Exception e) {
            Logger.e(TAG, "setTodo error " + e);
        }
        triggerTextChange();
        if (null == mEditText) {
            return;
        }
        RichTextKTUtilsKt.recordApplyStyleToUndoRedo(selectionStart, selectionEnd, getIsChecked(), mEditText, ARE_Upcoming.SPAN_CLASS);
        Logger.d(TAG, "ARE_Upcoming over onClick, spanSize=" + mEditText.getEditableText().getSpans(0, mEditText.getEditableText().length(), Object.class).length);
    }

    public void triggerTextChange() {
        sTriggerTextChange(mEditText);
    }

    private static void sTriggerTextChange(AREditText editText) {
        int selectionStart = editText.getSelectionStart();
        int selectionEnd = editText.getSelectionEnd();

        Editable editable = editText.getText();
        Logger.d("triggerTextChange, select start=" + selectionStart + " end=" + selectionEnd);
        editable.insert(selectionEnd, Constants.ZERO_WIDTH_SPACE_STR);
        editText.startStorageMonitor();
        editable.delete(selectionEnd, min(editable.length(), selectionEnd + 1));
        editText.startApplyMonitor();
        editText.startUiShowMonitor();
    }

    @Override
    public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
        logAllBulletListItems(editable);
        UpcomingListSpan[] listSpans = editable.getSpans(start, end,
                UpcomingListSpan.class);
        if (null == listSpans || listSpans.length == 0) {
            return;
        }

        Logger.d("UpcomingListSpan applyStyle start/end=" + start + "/" + end + " length=" + listSpans.length);

        if (end > start) {
            //
            // User inputs
            //
            // To handle the \n case

            // int totalLen = editable.toString().length();
            // Util.log("ListNumber - total len == " + totalLen);
            char c = editable.charAt(end - 1);
            if (c == Constants.CHAR_NEW_LINE) {
                int listSpanSize = listSpans.length;
                int previousListSpanIndex = listSpanSize - 1;
                if (previousListSpanIndex > -1) {
                    UpcomingListSpan previousListSpan = listSpans[previousListSpanIndex];
                    int lastListItemSpanStartPos = editable
                            .getSpanStart(previousListSpan);
                    int lastListItemSpanEndPos = editable
                            .getSpanEnd(previousListSpan);
                    CharSequence listItemSpanContent = editable.subSequence(
                            lastListItemSpanStartPos, lastListItemSpanEndPos);

                    Logger.d("handle newline span,start/end=" + lastListItemSpanStartPos + "/" + lastListItemSpanEndPos + " content=" + listItemSpanContent);
                    if (isEmptyListItemSpan(listItemSpanContent)) {
                        //
                        // Handle this case:
                        // 1. A
                        // 2. <User types \n here, at an empty span>
                        //
                        // The 2 chars are:
                        // 1. ZERO_WIDTH_SPACE_STR
                        // 2. \n
                        //
                        // We need to remove current span and do not re-create
                        // span.
                        editable.removeSpan(previousListSpan);

                        //
                        // Deletes the ZERO_WIDTH_SPACE_STR and \n
                        editable.delete(lastListItemSpanStartPos,
                                lastListItemSpanEndPos);
                        updateCheckStatus(false);
                        mEditText.post(new Runnable() {
                            @Override
                            public void run() {
                                if (mEditText != null) {
                                    int selStart = mEditText.getSelectionStart();
                                    int selEnd = mEditText.getSelectionEnd();
                                    mEditText.onSelectionChanged(selStart, selEnd);
                                }
                            }
                        });
                        return;
                    } else {
                        //
                        // Handle this case:
                        //
                        // 1. A
                        // 2. C
                        // 3. D
                        //
                        // User types \n after 'A'
                        // Then
                        // We should see:
                        // 1. A
                        // 2.
                        // 3. C
                        // 4. D
                        //
                        // We need to end the first span
                        // Then start the 2nd span
                        // Then reNumber the following list item spans
                        if (end > lastListItemSpanStartPos) {
                            removeStrikeboundSpan(lastListItemSpanStartPos, lastListItemSpanEndPos);
                            editable.removeSpan(previousListSpan);
                            if (previousListSpan.isChecked())
                                setStrikeboundSpan(lastListItemSpanStartPos, end - 1);
                            editable.setSpan(previousListSpan,
                                    lastListItemSpanStartPos, end - 1,
                                    Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                        }
                    }
                    makeLineAsUpcoming();
                } // #End of if it is in ListItemSpans..
            } // #End of user types \n
        } else {
            //
            // User deletes
            UpcomingListSpan theFirstSpan = listSpans[0];
            if (listSpans.length > 0) {
                FindFirstAndLastUpcomingSpan findFirstAndLastBulletSpan = new FindFirstAndLastUpcomingSpan(editable, listSpans).invoke();
                theFirstSpan = findFirstAndLastBulletSpan.getFirstTargetSpan();
            }
            int spanStart = editable.getSpanStart(theFirstSpan);
            int spanEnd = editable.getSpanEnd(theFirstSpan);

            Util.log("Delete spanStart = " + spanStart + ", spanEnd = " + spanEnd);

            if (spanStart >= spanEnd) {
                Util.log("case 1");
                //
                // User deletes the last char of the span
                // 检查span是否只剩下零宽字符，如果是则删除span
                boolean shouldRemoveSpan = true;
                if (spanStart > 0 && spanStart <= editable.length()) {
                    // 检查span的内容，如果只有零宽字符则删除，否则保留
                    String spanContent = editable.subSequence(Math.max(0, spanStart - 1), spanStart).toString();
                    if (!spanContent.equals(Constants.ZERO_WIDTH_SPACE_STR)) {
                        shouldRemoveSpan = false;
                    }
                }

                if (shouldRemoveSpan) {
                    // 删除span和相关样式
                    for (UpcomingListSpan listSpan : listSpans) {
                        editable.removeSpan(listSpan);
                    }

                    if (spanStart == spanEnd) {
                        removeStrikeboundSpan(spanStart, spanEnd);
                    }

                    // 删除零宽字符
                    if (spanStart > 0) {
                        editable.delete(spanStart - 1, spanEnd);
                    }
                } else {
                    // 不删除span，让用户可以再次删除来移除待办事项前缀
                    return;
                }
            } else if (start == spanStart) {
                // 光标在第一行行首 → 删除前缀
                removeUpcomingPrefix(editable, theFirstSpan, spanStart, spanEnd);
                return;
            }
            else if (isAtLineStartOfMultiLineSpan(editable, theFirstSpan, start)) {
                // 光标在跨行 span 的非第一行行首 → 只删零宽字符
                if (start < editable.length() && editable.charAt(start) == Constants.ZERO_WIDTH_SPACE_INT) {
                    editable.delete(start, start + 1);
                }
                // 无论删没删，都不要再继续走复杂删除逻辑
                return;
            } else if (start == spanEnd) {
                Util.log("case 3");
                //
                // User deletes the first char of the span
                // So we think he wants to remove the span
                if (editable.length() > start) {
                    if (editable.charAt(start) == Constants.CHAR_NEW_LINE) {
                        // The error case to handle
                        Util.log("case 3-1");
                        UpcomingListSpan[] spans = editable.getSpans(start, start, UpcomingListSpan.class);
                        Util.log(" spans len == " + spans.length);
                        if (spans.length > 0) {
                            mergeForward(editable, theFirstSpan, spanStart, spanEnd);
                        }
                    } else {
                        mergeForward(editable, theFirstSpan, spanStart, spanEnd);
                    }
                }
            } else if (start > spanStart && end < spanEnd) {
                //
                // Handle this case:
                // *. AAA1
                // *. BBB2
                // *. CCC3
                //
                // User deletes '1' / '2' / '3'
                // Or any other character inside of a span
                //
                // For this case we won't need do anything
                // As we need to keep the span styles as they are
                return;
            }
        }

        logAllBulletListItems(editable);
    }

    /**
     * 删除待办前缀（复选框和零宽字符等），并移除样式
     */
    private void removeUpcomingPrefix(Editable editable, UpcomingListSpan span, int spanStart, int spanEnd) {
        if (span != null) {
            editable.removeSpan(span);
            removeStrikeboundSpan(spanStart, spanEnd);
            updateCheckStatus(false);

            // 删除零宽字符
            if (editable.length() > spanStart &&
                    editable.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
                editable.delete(spanStart, spanStart + 1);
            }
        }
    }

    /**
     * 判断是否位于跨行待办span的非第一行行首
     */
    private boolean isAtLineStartOfMultiLineSpan(Editable editable, UpcomingListSpan span, int pos) {
        if (span == null) return false;
        int spanStart = editable.getSpanStart(span);
        int spanEnd = editable.getSpanEnd(span);
        if (spanStart < 0 || spanEnd < 0) return false;

        Layout layout = mEditText.getLayout();
        if (layout == null) return false;

        int startLine = layout.getLineForOffset(spanStart);
        int endLine = layout.getLineForOffset(spanEnd);
        if (startLine == endLine) return false; // 不是多行

        int currentLine = layout.getLineForOffset(pos);
        int currentLineStart = layout.getLineStart(currentLine);

        // 行首 && 当前行不是第一行 && 当前行在span范围内
        return pos == currentLineStart && currentLine > startLine &&
                pos >= spanStart && pos < spanEnd;
    }

    @Override
    public void removeStyle(Editable editable, int start, int end) {
        UpcomingListSpan[] listSpans = editable.getSpans(start, end,
                UpcomingListSpan.class);
        ForegroundColorSpan[] foregroundColorSpans = editable.getSpans(start, end,
                ForegroundColorSpan.class);
        StrikethroughSpan[] strikethroughSpans = editable.getSpans(start, end,
                StrikethroughSpan.class);
        if (null != listSpans) {
            for(UpcomingListSpan span : listSpans) {
                editable.removeSpan(span);
            }
        }
        if (null != foregroundColorSpans) {
            for(ForegroundColorSpan span : foregroundColorSpans) {
                editable.removeSpan(span);
            }
        }
        if (null != strikethroughSpans) {
            for(StrikethroughSpan span : strikethroughSpans) {
                editable.removeSpan(span);
            }
        }

    }

    private void recordApplyStyle(int start, int end, Boolean isFromStyleClick) {

    }

    @Override
    public void setStyleStatusListener(StyleStatusListener listener) {

    }

    @Override
    public Boolean needApplyStyle() {
        return true;
    }

    @Override
    public ImageView getImageView() {
        return mUpcomingView;
    }

    @Override
    public void setChecked(boolean isChecked) {
        mIsUpcomingCheck = isChecked;
    }

    @Override
    public boolean getIsChecked() {
        return mIsUpcomingCheck;
    }

    @Override
    public void setisValid(boolean isValid) {
        mIsUpcomingValid = isValid;
    }

    @Override
    public boolean getIsValid() {
        return mIsUpcomingValid;
    }

    @Override
    public void setEditText(AREditText editText) {
        this.mEditText = editText;
    }

    @Override
    public EditText getEditText() {
        return this.mEditText;
    }

    @Override
    public void updateCheckStatus(boolean checked) {
        setChecked(checked);
        if (getImageView() != null)
            getImageView().setSelected(checked);
    }

    protected void mergeForward(Editable editable, UpcomingListSpan listSpan, int spanStart, int spanEnd) {
        Logger.d("upcoming", "merge forward 1");
        if (editable.length() < spanEnd + 1) {
            return;
        }
        Logger.d("upcoming", "merge forward 2");
        UpcomingListSpan[] targetSpans = editable.getSpans(
                spanEnd, spanEnd + 1, UpcomingListSpan.class);
        if (targetSpans == null || targetSpans.length == 0) {
            return;
        }

        for (int s = spanEnd; s < editable.length(); s++) {
//            Logger.d("for s=" + s + " c=" + editable.charAt(s));
            if (editable.charAt(s) == Constants.ZERO_WIDTH_SPACE_INT) {
                mEditText.stopAllMonitor();
                editable.delete(s, s + 1);
                mEditText.startAllMonitor();
                targetSpans = editable.getSpans(
                        spanEnd, spanEnd + 1, UpcomingListSpan.class);
                if (targetSpans == null || targetSpans.length == 0) {
                    return;
                }
            }
        }

        FindFirstAndLastUpcomingSpan findFirstAndUpcomingSpan
                = new FindFirstAndLastUpcomingSpan(editable, targetSpans).invoke();
        UpcomingListSpan firstTargetSpan = findFirstAndUpcomingSpan.getFirstTargetSpan();
        UpcomingListSpan lastTargetSpan = findFirstAndUpcomingSpan.getLastTargetSpan();
        int targetStart = editable.getSpanStart(firstTargetSpan);
        int targetEnd = editable.getSpanEnd(lastTargetSpan);

        // 检查目标span是否仍然有效
        if (targetStart < 0 || targetEnd < 0 || targetStart > targetEnd) {
            Logger.d("upcoming","merge forward failed: target spans are invalid");
            return;
        }

        Logger.d("upcoming", "merge to remove span start == " + targetStart + ", target end = " + targetEnd);

        int targetLength = targetEnd - targetStart;
        spanEnd = spanEnd + targetLength;

        // 检查最终的span范围是否有效
        if (spanStart < 0 || spanEnd < 0 || spanStart > spanEnd || spanEnd > editable.length()) {
            Logger.d("upcoming","merge forward failed: final span range is invalid");
            return;
        }

        removeStrikeboundSpan(targetStart, targetEnd);
        for (UpcomingListSpan targetSpan : targetSpans) {
            editable.removeSpan(targetSpan);
        }
        UpcomingListSpan[] compositeSpans = editable.getSpans(spanStart, spanEnd, UpcomingListSpan.class);
        for (UpcomingListSpan lns : compositeSpans) {
            editable.removeSpan(lns);
        }
        editable.setSpan(listSpan, spanStart, spanEnd, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        Logger.d("upcoming", "merge span start == " + spanStart + " end == " + spanEnd);
    }

    private void logAllBulletListItems(Editable editable) {
        UpcomingListSpan[] listItemSpans = editable.getSpans(0,
                editable.length(), UpcomingListSpan.class);
        for (UpcomingListSpan span : listItemSpans) {
            int ss = editable.getSpanStart(span);
            int se = editable.getSpanEnd(span);
            Util.log("List All: " + " :: start == " + ss + ", end == " + se);
        }
    }

    /**
     * Check if this is an empty span.
     *
     * <B>OLD COMMENT: and whether it is at the end of the spans list</B>
     *
     * @param listItemSpanContent
     * @return
     */
    private boolean isEmptyListItemSpan(CharSequence listItemSpanContent) {
        int spanLen = listItemSpanContent.length();
        if (spanLen == 1 && listItemSpanContent.charAt(0) == Constants.CHAR_NEW_LINE) {
            return true;
        } else if (spanLen == 2
                && listItemSpanContent.charAt(0) == Constants.ZERO_WIDTH_SPACE_INT
                && listItemSpanContent.charAt(1) == Constants.CHAR_NEW_LINE) {
            //
            // This case:
            // 1. A
            // 2.
            //
            // Line 2 is empty
            return true;
        } else {
            return false;
        }
    }

    /**
     * Make the current line as an upcoming list item.
     *
     * @return
     */
    private UpcomingListSpan makeLineAsUpcoming() {
        int currentLine = Util.getCurrentCursorLine(mEditText);
        int start = Util.getThisLineStart(mEditText, currentLine);
        int end = Util.getThisLineEnd(mEditText, currentLine);
        Editable editable = mEditText.getText();

        Logger.d("111 makeLineAsUpconming start/end=" + start + "/" + end);

        // 确保行开始位置有零宽字符作为锚点
        // 这样无论是空行还是有文字的行，都有稳定的span结构
        if (!(end > start)) {
            // 空行：插入零宽字符
            editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
        } else {
            // 有文字的行：检查开始位置是否已有零宽字符，如果没有则插入
            if (start < editable.length() && editable.charAt(start) != Constants.ZERO_WIDTH_SPACE_INT) {
                editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
            }
        }

        start = Util.getThisLineStart(mEditText, currentLine);
        end = Util.getThisLineEnd(mEditText, currentLine);

        if (end > 0 && editable.charAt(end - 1) == Constants.CHAR_NEW_LINE) {
            end--;
        }

        // 边界保护
        if (start < 0) start = 0;
        if (end > editable.length()) end = editable.length();
        if (end < start) {
            Logger.w("ARE_Upcoming", "makeLineAsUpcoming: invalid range start=" + start + ", end=" + end + ", force adjusting.");
            end = start;
        }

        Logger.d("222 makeLineAsUpconming start/end=" + start + "/" + end);
        UpcomingListSpan upcomingListSpan = new UpcomingListSpan(RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Edit());
        editable.setSpan(upcomingListSpan, start, end,
                Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        return upcomingListSpan;
    }

    /**
     * Change the selected {@link ListNumberSpan} to {@link UpcomingListSpan}
     *
     * @param listNumberSpans
     */
    private void changeListNumberSpanToUpcomingSpan(
            Editable editable,
            ListNumberSpan[] listNumberSpans) {

        if (null == listNumberSpans || listNumberSpans.length == 0) {
            return;
        }

        // -
        // Handle this case:
        // User has:
        //
        // 1. AA
        // 2. BB
        // 3. CC
        // 4. DD
        //
        // Then user clicks Bullet icon at line 2:
        //
        // So it should change to:
        // 1. AA
        // * BB
        // 1. CC
        // 2. DD
        //
        // So this is for handling the line after 2nd line.
        // "CC" starts from 1 again.
        //
        // - Restart the count after the bullet span
        int len = listNumberSpans.length;
        ListNumberSpan lastListNumberSpan = listNumberSpans[len - 1];
        int lastListNumberSpanEnd = editable.getSpanEnd(lastListNumberSpan);

        ARE_ListNumber.reNumberBehindListItemSpans(lastListNumberSpanEnd + 1, editable, 0);

        //
        // - Replace all ListNumberSpan to UpcomingSpan
        for (ListNumberSpan listNumberSpan : listNumberSpans) {
            int start = editable.getSpanStart(listNumberSpan);
            int end = editable.getSpanEnd(listNumberSpan);

            editable.removeSpan(listNumberSpan);
            UpcomingListSpan upcomingListSpan = new UpcomingListSpan(RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Edit());
            editable.setSpan(upcomingListSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        }
    }

    private void changeListBulletSpanToUpComingSpan(Editable editable, ListBulletSpan[] listBulletSpans) {
        // - Restart the count after the bullet span
        int len = listBulletSpans.length;
        ListBulletSpan listBulletSpan = listBulletSpans[len - 1];
        int lastListUpcomingSpanEnd = editable.getSpanEnd(listBulletSpan);

//        // -- Change the content to trigger the editable redraw
//        editable.insert(lastListUpcomingSpanEnd, Constants.ZERO_WIDTH_SPACE_STR);
//        editable.delete(lastListUpcomingSpanEnd + 1, lastListUpcomingSpanEnd + 1);
//        // -- End: Change the content to trigger the editable redraw

        // - Replace all ListUpcomingSpan to ListBulletSpan
        for (ListBulletSpan bulletSpan : listBulletSpans) {
            int start = editable.getSpanStart(bulletSpan);
            int end = editable.getSpanEnd(bulletSpan);

            editable.removeSpan(bulletSpan);
            UpcomingListSpan upcomingListSpan = new UpcomingListSpan(RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Edit());
            editable.setSpan(upcomingListSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        }
    }


    private class FindFirstAndLastUpcomingSpan {
        private Editable editable;
        private UpcomingListSpan[] targetSpans;
        private UpcomingListSpan firstTargetSpan;
        private UpcomingListSpan lastTargetSpan;

        public FindFirstAndLastUpcomingSpan(Editable editable, UpcomingListSpan... targetSpans) {
            this.editable = editable;
            this.targetSpans = targetSpans;
        }

        public UpcomingListSpan getFirstTargetSpan() {
            return firstTargetSpan;
        }

        public UpcomingListSpan getLastTargetSpan() {
            return lastTargetSpan;
        }

        public FindFirstAndLastUpcomingSpan invoke() {
            firstTargetSpan = targetSpans[0];
            lastTargetSpan = targetSpans[0];
            if (targetSpans.length > 0) {
                int firstTargetSpanStart = editable.getSpanStart(firstTargetSpan);
                int lastTargetSpanEnd = editable.getSpanEnd(firstTargetSpan);
                for (UpcomingListSpan lns : targetSpans) {
                    int lnsStart = editable.getSpanStart(lns);
                    int lnsEnd = editable.getSpanEnd(lns);
                    if (lnsStart < firstTargetSpanStart) {
                        firstTargetSpan = lns;
                        firstTargetSpanStart = lnsStart;
                    }
                    if (lnsEnd > lastTargetSpanEnd) {
                        lastTargetSpan = lns;
                        lastTargetSpanEnd = lnsEnd;
                    }
                }
            }
            return this;
        }
    }


    public static boolean isTouchSpan(MotionEvent event, AREditText editText) {
        if (event.getAction() == MotionEvent.ACTION_MOVE) {
            return false;
        }
        int x = (int) event.getX();
        int y = (int) event.getY();

        x -= editText.getTotalPaddingLeft();
        y -= editText.getTotalPaddingTop();

        x += editText.getScrollX();
        y += editText.getScrollY();

        Layout layout = editText.getLayout();
        int line = layout.getLineForVertical(y);
        int off = layout.getOffsetForHorizontal(line, x);
        int lineBottom = layout.getLineBottom(line);

        UpcomingListSpan[] link = editText.getText().getSpans(off, off, UpcomingListSpan.class);
        if (link.length != 0) {
            if (link[0].isClickValid(editText, event, lineBottom)) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    link[0].setChecked(!link[0].isChecked());
                    ARE_Upcoming upcoming = new ARE_Upcoming(editText.getContext());
                    upcoming.setEditText(editText);
                    upcoming.toggleStrikeboundSpan(link[0].isChecked(), link[0]);
//                    sTriggerTextChange(editText);
                    Runnable task = editText.getSaveContentToMemoryTask();
                    if (task != null) {
                        task.run();
                    }
                }
                return true;
            }
        }
        return false;
    }


}
