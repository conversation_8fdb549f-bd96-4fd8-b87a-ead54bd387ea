package com.tcl.ai.note.handwritingtext.ui.categorydialog

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.data.GetUserSettingUseCase
import com.tcl.ai.note.data.UserSavedCategoryInfo
import com.tcl.ai.note.handwritingtext.repo.HomeRepository
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.utils.determineIcon
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *分类弹窗的Dialog ViewModel
 */
class CategoryDialogViewModel() : ViewModel() {


    private val TAG = "CategoryDialogViewModel"

    private val _categories = MutableStateFlow<List<DialogCategory>>(emptyList())
    val categories: StateFlow<List<DialogCategory>> = _categories.asStateFlow()

    private val _categoryDialogState = MutableStateFlow(
        CategoryDialogState()
    )
    val categoryDialogState: StateFlow<CategoryDialogState> = _categoryDialogState.asStateFlow()

    val categoryDialogController = CategoryDialogController(_categoryDialogState)

    // 跟踪对话框的显示状态，避免第一次进入时误触发dismiss事件
    private var hasBeenVisible = false

    init {
        viewModelScope.launch {
            _categoryDialogState.collect { state ->
                Logger.d(TAG, "Dialog state changed: isVisible=${state.isVisible}, hasBeenVisible=$hasBeenVisible")

                when {
                    // 对话框从隐藏变为显示
                    state.isVisible && !hasBeenVisible -> {
                        Logger.d(TAG, "Dialog shown for the first time")
                        hasBeenVisible = true
                    }
                    // 对话框从显示变为隐藏，且之前已经显示过
                    !state.isVisible && hasBeenVisible -> {
                        Logger.d(TAG, "Dialog dismissed after being visible")
                        sendDialogDismissEvent()
                        hasBeenVisible = false // 重置状态，为下次显示做准备
                    }
                    // 初始状态或其他情况
                    else -> {
                        Logger.d(TAG, "Dialog state: initial or other case")
                    }
                }
            }
        }
    }
    fun observeCategoryListAndDialogState() {
        observeCategoryDialogEvents()
        observeCategoryList()
    }
    var categoryListJob: Job? = null
    fun observeCategoryList() {
            categoryListJob?.cancel()
         categoryListJob = viewModelScope.launch {
            // 直接监听分类列表变化，已经内置了笔记数量监听
             HomeRepository.getAllCategoriesList()
                .catch { emit(Pair(emptyList(), 0)) }
                .flowOn(Dispatchers.IO)
                .collect { categories ->
                    _categoryDialogState.update {
                        it.copy(noteCategories = mapToCategoriesList(categories))
                    }
                }
        }
    }


    private fun mapToCategoriesList(savedNoteValue: Pair<List<NoteCategory>, Int>): List<DialogCategory> {
        val totalNoteCount = savedNoteValue.second
        val savedCategories = savedNoteValue.first

        Logger.d(TAG, "loadCategoryList: totalNoteCount: $totalNoteCount  savedCategories: ${savedCategories.map { it.noteCounts }} " + Thread.currentThread().name)
        val categoryItems = savedCategories.toMutableList().apply {
            // 1. 查找id为"1"的项既：“未分类”（注意id是字符串类型，需与实际类型匹配）
            val targetItem = find { it.categoryId == 1L }
            // 2. 如果找到目标项，先移除再插入到索引0的位置
            if (targetItem != null) {
                remove(targetItem)
                add(0, targetItem)
            }
        }
        return categoryItems.map { category ->
            DialogCategory(
                icon = determineIcon(category),
                categoryId = category.categoryId,
                name = category.name,
                colorIndex = category.colorIndex,
                color = null,
                noteCounts = category.noteCounts,
            )
        }
    }
    var eventJob: Job? = null

     fun observeCategoryDialogEvents() {
        // 取消之前的Job
        eventJob?.cancel()
        eventJob = viewModelScope.launch {
            CategoryDialogEventManager.categoryDialogEvents.collect { event ->
                when (event.type) {
                    CategoryDialogType.NEW_CATEGORY -> {
                        categoryDialogController.showNewCategoryDialog(false)
                    }

                    CategoryDialogType.EDIT_CATEGORY -> {
                        event.dialogCategory?.let {
                            categoryDialogController.showEditCategoryDialog(
                                it
                            )
                        }
                    }

                    CategoryDialogType.MOVE_NOTE_TO_CATEGORY -> {
                        categoryDialogController.showMoveNotesDialog(event.dialogNotes,event.categoryId,event.isPreviewMode)
                    }
                }
            }
        }
    }


    fun addCategory(newCategory: NoteCategory,noteIds: List<Long>, isPreviewMode: Boolean) = viewModelScope.launch {
        Logger.i(TAG,"addCategory, newCategory:$newCategory, isPreviewMode:$isPreviewMode, noteIds.size:${noteIds.size}")
        val categoryId = HomeRepository.addCategory(newCategory)
        if (categoryId > 0) {
            if (isPreviewMode) {
                updateNotesCategoryId(noteIds, categoryId,true)
            }
            CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnCategoryCreatedCallBack(categoryId.toString()))
        }
    }

    /**
     * 重命名一个Category
     */
    fun renameCategory(noteCategory: NoteCategory) = viewModelScope.launch {
        Logger.i(TAG,"renameCategory, rename, noteCategory:$noteCategory")
        HomeRepository.getCategory(noteCategory.categoryId)?.apply {
            //重命名不修改创建时间，不修改更新时间，避免修改名称后自动跑到第一个位置
            val newCategory=noteCategory.copy(createTime= this.createTime, modifyTime = this.modifyTime)
            HomeRepository.updateCategory(newCategory)
        }
    }

    /**
     * 更新指定 Note 的 categoryId
     */
    fun updateNotesCategoryId(listNote: List<Long>, categoryId: Long, isPreviewMode:Boolean) =
        viewModelScope.launch {
            Logger.i(TAG,"updateNotesCategoryId, categoryId:$categoryId,isPreviewMode:$isPreviewMode, listNote.size:${listNote.size}")
            HomeRepository.updateNotesCategoryId(listNote, categoryId)
            // 将缓存中的当前分类信息设置为移动后的分类
            val category = HomeRepository.getCategory(categoryId)
            category?.let {
                if (!isPreviewMode){
                    CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnCategoryNoteMoved(categoryId.toString()))
                }else{
                    CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnNoteMovedSuccessful(categoryId.toString()))
                }
            }
        }


    fun onCategoryDialogDismiss() {
        Logger.d(TAG, "onCategoryDialogDismiss called")
        _categoryDialogState.update { it.copy(isVisible = false) }
    }

    private fun sendDialogDismissEvent() {
        viewModelScope.launch {
            Logger.i(TAG, "onCategoryDialogDismiss")
            CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnCategoryDialogDismiss())
        }
    }

    /**
     * 重置弹窗状态
     */
    fun resetDialogState() {
        Logger.i(TAG,"resetDialogState")
        eventJob?.cancel()
//        categoryListJob?.cancel()//保持分类列表的监听
    }

}