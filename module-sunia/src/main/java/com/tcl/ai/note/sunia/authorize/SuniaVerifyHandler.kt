package com.tcl.ai.note.sunia.authorize

import com.sunia.authlib.bean.DeviceIdType
import com.sunia.authlib.bean.URLType
import com.sunia.authlib.bean.VerifyData
import com.sunia.authlib.managers.AuthManager
import com.sunia.penengine.sdk.engine.VerifyInfo
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isLiteVersion
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.suspendCoroutineWithTimeoutOrNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlin.coroutines.resume

object SuniaVerifyHandler {
    private val _suniaVerifyStateFlow = MutableStateFlow<VerifyInfo?>(null)
    val suniaVerifyStateFlow = _suniaVerifyStateFlow.asStateFlow()

    init {
        GlobalContext.Companion.applicationScope.launch(Dispatchers.IO) {
            while (suniaVerifyStateFlow.value == null) {
                _suniaVerifyStateFlow.value = getVerifyInfo()
                delay(3000)
            }
        }
    }

    private suspend fun getVerifyInfo() =
        suspendCoroutineWithTimeoutOrNull(3000) { cont ->
            val context = GlobalContext.Companion.instance
            val verifyDir = context.getExternalFilesDir(VERIFY_INFO_DIR)
            if (verifyDir == null) {
                cont.resume(null)
                return@suspendCoroutineWithTimeoutOrNull
            }
            if (!verifyDir.exists()) {
                if (!verifyDir.mkdirs()) {
                    cont.resume(null)
                    return@suspendCoroutineWithTimeoutOrNull
                }
            }
            val encryptPath = verifyDir.absolutePath + "/" + LICENSE_FILE
            val verifyData = VerifyData(APP_ID, PUBLIC_KEY, encryptPath, OFFLINE_CERT).apply {
                deviceIdType = DeviceIdType.ANDROID_ID
                urlType = URLType.PROD.value
            }
            Logger.v(TAG, "getVerifyInfo, isTablet: $isTablet, isLiteVersion: $isLiteVersion")

            // 打开鉴权日志
            //AuthManager.getInstance().setEnableLog(
            //    true,
            //    verifyDir.absolutePath,
            //    "authLogUtil.txt"
            //)

            // 开始鉴权
            AuthManager.getInstance().authorize(context, verifyData) { _, p1, _ ->
                Logger.v(TAG, "OnAuthorResultCallback: $p1 APP_ID $APP_ID")
                cont.resume(
                    VerifyInfo(encryptPath).apply {
                        appId = APP_ID
                        deviceIdType = DeviceIdType.ANDROID_ID.value
                    }
                )
            }
        }

    private const val TAG = "SuniaVerifyHandler"
    private const val VERIFY_INFO_DIR = "verifyInfos"
    private const val LICENSE_FILE = "license_auth_cert.text"
    private const val APP_ID = "20250380110525152"
    private val OFFLINE_CERT = when {
       isTablet -> "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"
       isLiteVersion -> "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"
       else -> "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"
    }
    private val PUBLIC_KEY = when {
        isTablet -> "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmk8l/bJjaByugOGgUMb7AhtYGyZ3rvZPQ2ueFeDzK+8HiKdysmAa+VUBfAOPeT3eOHthGLd4ZzTvNQQy6lvfAnB0HPeduOMhlNaHHzER4ZpoXgDGyQPaWScQWFts8oDW4pY4MAzx2qn3FnDW8/N/n0hwaG8EdHK3CU5Nzz7X53UHnQtaTJ9lVuZISj7A0rhORps8/Ay9EWZDtA5QlSBC/2dvp6IjgvOXOuR62AXkN925jUg4dsd2CFBAb3oSn8vJH5X1Zv/6TMjhZ6+P0ZionLGYk/EIim3UDB5ANLXg4OSDDmQ8II+R8262ewkdu5EMEwoXWyn6t9NIQb67E2fm0wIDAQAB"
        isLiteVersion -> "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlspNQpG9gDzbbMaM4YGFoLU4X+2koglTneyToFzXEIXKgNTxLeq7ZhHrj+NrDuoc1/SpuHxmOURTDHppYzVnPN4PO4b3ZFuvRcJ14YDyUqVHb5zw/oLf0ir99frtYE/d8HwrNF59oApmyZ2BRnPinf0mBnaVgijK107LfiSCm60AyRO5CFZiC4qe4SR6xoxb7A8/iOB2s7U9URs0Gdw+e+KcFIDyS5IrcpDKeSa0+jtbaeWpfE6YNvr8G9yCg1IvJCxFd/5MWuEDDrOVqAGuNoxndKcOgmJxsiXIPQO1fmSxMcgHOlO3submiaxvdCHoFu3mUm0kuPtELwEx5ak+RwIDAQAB"
        else -> "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq83bBehqtSY4cwSJmGjOifNOfvInzSCZ4kiIu6/PqaXmhxLyE0sRTba7S01S4EJnign1yWrU3Ipjzu8KUGzI89ROol7JDibcTKvGlelJY8STUYDKZXMVcfBJHwB5+j7QjNPgXV6KVvZYsbuzniB7Ba8xS1hyMc4SGFJYq4xj3h1BAKDU/aVplTXhQSqsV83Ojv5o/XEtium4PY0YuzsxV8ru5IwYPlopKM7RF3BWfAwxBTk5wF5/1vz7T5J5eE20NV4vtIYT0wL3z817IcXum+rCkP2jcHldmVLv0FPBKlJDYczAH9hqTKgx340AP3CHXBG8BqRcAvXcHKveSyYCVwIDAQAB"
    }
}